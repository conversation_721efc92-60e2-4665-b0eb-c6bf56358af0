#!/usr/bin/env python3
"""
长江水质综合评价分析系统 - 增强版
Enhanced Yangtze River Water Quality Comprehensive Analysis System

主要改进：
1. 每张图表独立保存，提高清晰度
2. 优化评价体系，增加多种评价指标
3. 新增三维可视化分析
4. 增加敏感度分析
5. 添加统计分析和相关性分析

基于GB3838-2002《地表水环境质量标准》进行评价
数据来源：2003年6月-2005年9月长江水质监测数据

作者：长江水质分析系统增强版
日期：2025-07-28
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
from mpl_toolkits.mplot3d import Axes3D
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from scipy import stats
from scipy.stats import pearsonr
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
# 设置图表样式
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")


class YangtzeEnhancedWaterQualityAnalyzer:
    """长江水质综合评价分析器 - 增强版"""
    
    def __init__(self):
        """初始化分析器"""
        self.setup_logging()
        
        # 水质类别评分
        self.class_scores = {
            'I': 100, 'II': 80, 'III': 60, 'IV': 40, 'V': 20, '劣V': 0
        }
        
        # 指标权重（可调整用于敏感度分析）
        self.weights = {
            'do': 0.25,      # 溶解氧
            'codmn': 0.30,   # 高锰酸盐指数
            'nh3n': 0.30,    # 氨氮
            'ph': 0.15       # pH值
        }
        
        # GB3838-2002标准限值
        self.standards = {
            'I': {'do': 7.5, 'codmn': 2.0, 'nh3n': 0.15, 'ph': [6.0, 9.0]},
            'II': {'do': 6.0, 'codmn': 4.0, 'nh3n': 0.5, 'ph': [6.0, 9.0]},
            'III': {'do': 5.0, 'codmn': 6.0, 'nh3n': 1.0, 'ph': [6.0, 9.0]},
            'IV': {'do': 3.0, 'codmn': 10.0, 'nh3n': 1.5, 'ph': [6.0, 9.0]},
            'V': {'do': 2.0, 'codmn': 15.0, 'nh3n': 2.0, 'ph': [6.0, 9.0]}
        }
        
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.logger.info("长江水质综合评价分析器增强版初始化完成")
    
    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger('yangtze_enhanced_analyzer')
    
    def load_and_process_data(self, file_path: str = 'yangtze_complete_data_2003_2005.csv') -> pd.DataFrame:
        """
        加载和处理水质数据
        
        Args:
            file_path: 数据文件路径
            
        Returns:
            pd.DataFrame: 处理后的水质数据
        """
        try:
            # 加载数据
            df = pd.read_csv(file_path)
            df['date'] = pd.to_datetime(df['date'])
            
            # 数据清洗
            df = df.dropna(subset=['ph', 'do', 'codmn', 'nh3n'])
            
            self.logger.info(f"成功加载水质数据：{len(df)}条记录")
            self.logger.info(f"时间范围：{df['date'].min()} 至 {df['date'].max()}")
            self.logger.info(f"监测站点：{df['station'].nunique()}个")
            
            # 重新分类水质
            df['calculated_class'] = df.apply(self._classify_water_quality, axis=1)
            
            # 计算多种评价指标
            df = self._calculate_comprehensive_indices(df)
            
            # 时间相关字段
            df['year_month'] = df['date'].dt.to_period('M')
            df['season'] = df['date'].dt.month.map({
                12: '冬季', 1: '冬季', 2: '冬季',
                3: '春季', 4: '春季', 5: '春季',
                6: '夏季', 7: '夏季', 8: '夏季',
                9: '秋季', 10: '秋季', 11: '秋季'
            })
            df['year'] = df['date'].dt.year
            df['month'] = df['date'].dt.month
            df['day_of_year'] = df['date'].dt.dayofyear
            
            # 添加站点编码（用于3D可视化）
            stations = df['station'].unique()
            station_codes = {station: i for i, station in enumerate(stations)}
            df['station_code'] = df['station'].map(station_codes)
            
            self.logger.info("数据处理完成")
            return df
            
        except Exception as e:
            self.logger.error(f"数据加载失败：{e}")
            raise
    
    def _classify_water_quality(self, row: pd.Series) -> str:
        """
        根据GB3838-2002标准分类水质
        
        Args:
            row: 数据行
            
        Returns:
            str: 水质类别
        """
        ph, do, codmn, nh3n = row['ph'], row['do'], row['codmn'], row['nh3n']
        
        # pH值检查
        if ph < 6.0 or ph > 9.0:
            return '劣V'
        
        # 按最差指标确定类别
        if do < 2.0 or codmn > 15.0 or nh3n > 2.0:
            return '劣V'
        elif do < 3.0 or codmn > 10.0 or nh3n > 1.5:
            return 'V'
        elif do < 5.0 or codmn > 6.0 or nh3n > 1.0:
            return 'IV'
        elif do < 6.0 or codmn > 4.0 or nh3n > 0.5:
            return 'III'
        elif do < 7.5 or codmn > 2.0 or nh3n > 0.15:
            return 'II'
        else:
            return 'I'
    
    def _calculate_comprehensive_indices(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算综合评价指标"""
        df = df.copy()
        
        # 1. 基础WQI计算
        df['wqi'] = df['calculated_class'].map(self.class_scores)
        
        # 2. 污染指数计算（以III类标准为基准）
        df['do_pi'] = 5.0 / df['do']  # DO污染指数
        df['codmn_pi'] = df['codmn'] / 6.0  # CODMn污染指数
        df['nh3n_pi'] = df['nh3n'] / 1.0  # NH3-N污染指数
        
        # pH污染指数
        df['ph_pi'] = np.where(
            (df['ph'] >= 6.0) & (df['ph'] <= 9.0), 0,
            np.maximum(np.abs(df['ph'] - 6.0), np.abs(df['ph'] - 9.0))
        )
        
        # 3. 综合污染指数
        df['comprehensive_pi'] = (
            df['do_pi'] * self.weights['do'] +
            df['codmn_pi'] * self.weights['codmn'] +
            df['nh3n_pi'] * self.weights['nh3n'] +
            df['ph_pi'] * self.weights['ph']
        )
        
        # 4. 生态风险评估指数
        df['ecological_risk'] = self._calculate_ecological_risk(df)
        
        # 5. 水质稳定性指数
        df = self._calculate_stability_index(df)
        
        # 6. 富营养化指数
        df['eutrophication_index'] = self._calculate_eutrophication_index(df)
        
        # 7. 污染程度分级
        df['pollution_level'] = df['wqi'].apply(self._get_pollution_level)
        df['risk_level'] = df['ecological_risk'].apply(self._get_risk_level)
        
        return df
    
    def _calculate_ecological_risk(self, df: pd.DataFrame) -> pd.Series:
        """计算生态风险评估指数"""
        # 基于多指标的生态风险评估
        risk_scores = []
        for _, row in df.iterrows():
            risk = 0
            # DO风险
            if row['do'] < 3.0:
                risk += 3
            elif row['do'] < 5.0:
                risk += 2
            elif row['do'] < 6.0:
                risk += 1
            
            # 有机污染风险
            if row['codmn'] > 10.0:
                risk += 3
            elif row['codmn'] > 6.0:
                risk += 2
            elif row['codmn'] > 4.0:
                risk += 1
            
            # 氮污染风险
            if row['nh3n'] > 1.5:
                risk += 3
            elif row['nh3n'] > 1.0:
                risk += 2
            elif row['nh3n'] > 0.5:
                risk += 1
            
            risk_scores.append(risk)
        
        return pd.Series(risk_scores, index=df.index)
    
    def _calculate_stability_index(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算水质稳定性指数"""
        df = df.copy()
        
        # 按站点计算稳定性
        stability_scores = []
        for station in df['station'].unique():
            station_data = df[df['station'] == station]
            
            # 计算各指标的变异系数
            wqi_cv = station_data['wqi'].std() / station_data['wqi'].mean() if station_data['wqi'].mean() > 0 else 0
            do_cv = station_data['do'].std() / station_data['do'].mean() if station_data['do'].mean() > 0 else 0
            codmn_cv = station_data['codmn'].std() / station_data['codmn'].mean() if station_data['codmn'].mean() > 0 else 0
            nh3n_cv = station_data['nh3n'].std() / station_data['nh3n'].mean() if station_data['nh3n'].mean() > 0 else 0
            
            # 综合稳定性指数（变异系数越小，稳定性越高）
            stability = 100 - (wqi_cv + do_cv + codmn_cv + nh3n_cv) * 25
            stability = max(0, min(100, stability))  # 限制在0-100范围内
            
            # 为该站点的所有记录分配稳定性分数
            df.loc[df['station'] == station, 'stability_index'] = stability
        
        return df
    
    def _calculate_eutrophication_index(self, df: pd.DataFrame) -> pd.Series:
        """计算富营养化指数"""
        # 基于氨氮和有机物的富营养化评估
        eutro_scores = []
        for _, row in df.iterrows():
            score = 0
            # 氨氮贡献
            if row['nh3n'] > 2.0:
                score += 4
            elif row['nh3n'] > 1.0:
                score += 3
            elif row['nh3n'] > 0.5:
                score += 2
            elif row['nh3n'] > 0.15:
                score += 1
            
            # 有机物贡献
            if row['codmn'] > 10.0:
                score += 3
            elif row['codmn'] > 6.0:
                score += 2
            elif row['codmn'] > 4.0:
                score += 1
            
            eutro_scores.append(score)
        
        return pd.Series(eutro_scores, index=df.index)
    
    def _get_pollution_level(self, wqi: float) -> str:
        """获取污染程度等级"""
        if wqi >= 80:
            return '优秀'
        elif wqi >= 60:
            return '良好'
        elif wqi >= 40:
            return '轻度污染'
        elif wqi >= 20:
            return '中度污染'
        else:
            return '重度污染'
    
    def _get_risk_level(self, risk: float) -> str:
        """获取生态风险等级"""
        if risk <= 1:
            return '低风险'
        elif risk <= 3:
            return '中等风险'
        elif risk <= 6:
            return '高风险'
        else:
            return '极高风险'

    # ==================== 独立可视化方法 ====================

    def create_wqi_trend_chart(self, df: pd.DataFrame, temporal_results: Dict) -> str:
        """创建WQI时间趋势图"""
        fig, ax = plt.subplots(figsize=(14, 8))

        monthly_wqi = temporal_results['monthly_wqi']

        # 主趋势线
        ax.plot(range(len(monthly_wqi)), monthly_wqi.values,
               marker='o', linewidth=3, markersize=6, color='#2E86AB',
               label='月度WQI', alpha=0.8)

        # 添加趋势线
        if len(monthly_wqi) > 1:
            x_numeric = range(len(monthly_wqi))
            z = np.polyfit(x_numeric, monthly_wqi.values, 1)
            p = np.poly1d(z)
            ax.plot(x_numeric, p(x_numeric),
                   "r--", alpha=0.8, linewidth=2,
                   label=f'趋势线: {temporal_results["trend_direction"]}')

        # 添加水质等级参考线
        ax.axhline(y=80, color='green', linestyle=':', alpha=0.7, label='优秀水质线(80)')
        ax.axhline(y=60, color='orange', linestyle=':', alpha=0.7, label='良好水质线(60)')
        ax.axhline(y=40, color='red', linestyle=':', alpha=0.7, label='污染水质线(40)')

        # 填充区域
        ax.fill_between(range(len(monthly_wqi)), monthly_wqi.values, alpha=0.3, color='#2E86AB')

        ax.set_title('长江水质综合指数(WQI)时间变化趋势分析', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('时间序列（月份）', fontsize=12)
        ax.set_ylabel('WQI值', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=10)

        # 添加统计信息
        mean_wqi = monthly_wqi.mean()
        std_wqi = monthly_wqi.std()
        ax.text(0.02, 0.98, f'平均WQI: {mean_wqi:.1f}\n标准差: {std_wqi:.1f}',
               transform=ax.transAxes, fontsize=10, verticalalignment='top',
               bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()
        filename = f'问题1_WQI时间趋势分析_{self.timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()
        self.logger.info(f"WQI时间趋势图已保存：{filename}")
        return filename

    def create_station_comparison_chart(self, df: pd.DataFrame, spatial_results: Dict) -> str:
        """创建各站点WQI对比图"""
        fig, ax = plt.subplots(figsize=(14, 10))

        station_wqi = spatial_results['station_stats']['wqi']['mean'].sort_values()
        colors = ['#d62728' if x < 40 else '#ff7f0e' if x < 60 else '#ffbb78' if x < 80 else '#2ca02c'
                 for x in station_wqi.values]

        bars = ax.barh(range(len(station_wqi)), station_wqi.values, color=colors, alpha=0.8)

        # 设置y轴标签
        ax.set_yticks(range(len(station_wqi)))
        station_labels = [s[:12] + '...' if len(s) > 12 else s for s in station_wqi.index]
        ax.set_yticklabels(station_labels, fontsize=10)

        # 添加数值标签
        for i, (bar, value) in enumerate(zip(bars, station_wqi.values)):
            ax.text(value + 1, i, f'{value:.1f}',
                   va='center', fontsize=9, fontweight='bold')

        # 添加参考线
        ax.axvline(x=80, color='green', linestyle='--', alpha=0.7, label='优秀水质线')
        ax.axvline(x=60, color='orange', linestyle='--', alpha=0.7, label='良好水质线')
        ax.axvline(x=40, color='red', linestyle='--', alpha=0.7, label='污染水质线')

        ax.set_title('长江各监测站点水质综合指数(WQI)对比分析', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('WQI值', fontsize=12)
        ax.set_ylabel('监测站点', fontsize=12)
        ax.grid(True, alpha=0.3, axis='x')
        ax.legend(fontsize=10)

        # 添加统计信息
        best_station = station_wqi.idxmax()
        worst_station = station_wqi.idxmin()
        ax.text(0.02, 0.98, f'最佳站点: {best_station[:15]}... ({station_wqi.max():.1f})\n最差站点: {worst_station[:15]}... ({station_wqi.min():.1f})',
               transform=ax.transAxes, fontsize=10, verticalalignment='top',
               bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

        plt.tight_layout()
        filename = f'问题1_各站点WQI对比分析_{self.timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        self.logger.info(f"各站点WQI对比图已保存：{filename}")
        plt.close()
        return filename

    def create_water_class_distribution_chart(self, df: pd.DataFrame) -> str:
        """创建水质类别分布图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

        # 左图：饼图
        class_counts = df['calculated_class'].value_counts()
        colors_pie = ['#2ca02c', '#98df8a', '#ffbb78', '#ff7f0e', '#d62728', '#8c564b']

        wedges, texts, autotexts = ax1.pie(class_counts.values,
                                          labels=class_counts.index,
                                          colors=colors_pie[:len(class_counts)],
                                          autopct='%1.1f%%',
                                          startangle=90,
                                          explode=[0.05 if cls in ['劣V', 'V'] else 0 for cls in class_counts.index])

        ax1.set_title('水质类别分布比例', fontsize=14, fontweight='bold')

        # 右图：柱状图
        bars = ax2.bar(class_counts.index, class_counts.values,
                      color=colors_pie[:len(class_counts)], alpha=0.8)

        # 添加数值标签
        for bar, value in zip(bars, class_counts.values):
            ax2.text(bar.get_x() + bar.get_width()/2, value + 2,
                    f'{value}\n({value/len(df)*100:.1f}%)',
                    ha='center', va='bottom', fontsize=10, fontweight='bold')

        ax2.set_title('水质类别记录数量统计', fontsize=14, fontweight='bold')
        ax2.set_xlabel('水质类别', fontsize=12)
        ax2.set_ylabel('记录数量', fontsize=12)
        ax2.grid(True, alpha=0.3, axis='y')

        # 添加总体评价
        drinkable_rate = class_counts[class_counts.index.isin(['I', 'II', 'III'])].sum() / len(df) * 100
        polluted_rate = class_counts[class_counts.index.isin(['IV', 'V', '劣V'])].sum() / len(df) * 100

        fig.suptitle(f'长江水质类别分布分析\n可饮用水比例: {drinkable_rate:.1f}% | 污染水比例: {polluted_rate:.1f}%',
                    fontsize=16, fontweight='bold')

        plt.tight_layout()
        filename = f'问题1_水质类别分布分析_{self.timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        self.logger.info(f"水质类别分布图已保存：{filename}")
        plt.close()
        return filename

    def create_seasonal_analysis_chart(self, df: pd.DataFrame, temporal_results: Dict) -> str:
        """创建季节性分析图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

        # 左图：季节性WQI变化
        seasonal_wqi = temporal_results['seasonal_stats']['wqi']['mean']
        colors_season = ['#87CEEB', '#98FB98', '#FFA500', '#D2691E']

        bars1 = ax1.bar(seasonal_wqi.index, seasonal_wqi.values,
                       color=colors_season, alpha=0.8, width=0.6)

        # 添加数值标签
        for bar, value in zip(bars1, seasonal_wqi.values):
            ax1.text(bar.get_x() + bar.get_width()/2, value + 1,
                    f'{value:.1f}', ha='center', va='bottom',
                    fontsize=12, fontweight='bold')

        ax1.set_title('季节性水质变化', fontsize=14, fontweight='bold')
        ax1.set_ylabel('平均WQI值', fontsize=12)
        ax1.grid(True, alpha=0.3, axis='y')

        # 右图：季节性污染物浓度变化
        seasonal_pollutants = df.groupby('season')[['do', 'codmn', 'nh3n']].mean()

        x = np.arange(len(seasonal_pollutants.index))
        width = 0.25

        bars2_1 = ax2.bar(x - width, seasonal_pollutants['do'], width,
                         label='溶解氧(DO)', alpha=0.8, color='#2E86AB')
        bars2_2 = ax2.bar(x, seasonal_pollutants['codmn'], width,
                         label='高锰酸盐指数', alpha=0.8, color='#A23B72')
        bars2_3 = ax2.bar(x + width, seasonal_pollutants['nh3n'], width,
                         label='氨氮', alpha=0.8, color='#F18F01')

        ax2.set_title('季节性污染物浓度变化', fontsize=14, fontweight='bold')
        ax2.set_xlabel('季节', fontsize=12)
        ax2.set_ylabel('浓度 (mg/L)', fontsize=12)
        ax2.set_xticks(x)
        ax2.set_xticklabels(seasonal_pollutants.index)
        ax2.legend()
        ax2.grid(True, alpha=0.3, axis='y')

        # 添加季节性特征分析
        best_season = seasonal_wqi.idxmax()
        worst_season = seasonal_wqi.idxmin()

        fig.suptitle(f'长江水质季节性变化分析\n最佳季节: {best_season} (WQI={seasonal_wqi[best_season]:.1f}) | 最差季节: {worst_season} (WQI={seasonal_wqi[worst_season]:.1f})',
                    fontsize=16, fontweight='bold')

        plt.tight_layout()
        filename = f'问题1_季节性变化分析_{self.timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        self.logger.info(f"季节性分析图已保存：{filename}")
        plt.close()
        return filename

    def create_pollution_analysis_chart(self, df: pd.DataFrame, spatial_results: Dict) -> str:
        """创建污染状况分析图"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # 1. 污染程度分布
        pollution_dist = df['pollution_level'].value_counts()
        colors_pollution = ['#2ca02c', '#98df8a', '#ffbb78', '#ff7f0e', '#d62728']

        ax1.pie(pollution_dist.values, labels=pollution_dist.index,
               colors=colors_pollution[:len(pollution_dist)], autopct='%1.1f%%',
               startangle=90)
        ax1.set_title('污染程度分布', fontsize=14, fontweight='bold')

        # 2. 生态风险分布
        risk_dist = df['risk_level'].value_counts()
        colors_risk = ['#2ca02c', '#ffbb78', '#ff7f0e', '#d62728']

        ax2.pie(risk_dist.values, labels=risk_dist.index,
               colors=colors_risk[:len(risk_dist)], autopct='%1.1f%%',
               startangle=90)
        ax2.set_title('生态风险等级分布', fontsize=14, fontweight='bold')

        # 3. 主要污染物污染指数对比
        pollutant_means = df[['do_pi', 'codmn_pi', 'nh3n_pi']].mean()
        pollutant_names = ['溶解氧污染指数', '高锰酸盐污染指数', '氨氮污染指数']
        colors_bar = ['#87CEEB', '#FFA07A', '#98FB98']

        bars3 = ax3.bar(pollutant_names, pollutant_means.values,
                       color=colors_bar, alpha=0.8)

        # 添加数值标签
        for bar, value in zip(bars3, pollutant_means.values):
            ax3.text(bar.get_x() + bar.get_width()/2, value + 0.02,
                    f'{value:.2f}', ha='center', va='bottom',
                    fontsize=10, fontweight='bold')

        ax3.axhline(y=1.0, color='red', linestyle='--', alpha=0.7, label='标准线(1.0)')
        ax3.set_title('主要污染物污染指数对比', fontsize=14, fontweight='bold')
        ax3.set_ylabel('污染指数', fontsize=12)
        ax3.legend()
        ax3.grid(True, alpha=0.3, axis='y')
        ax3.tick_params(axis='x', rotation=45)

        # 4. 富营养化指数分布
        eutro_dist = df['eutrophication_index'].value_counts().sort_index()

        bars4 = ax4.bar(eutro_dist.index, eutro_dist.values,
                       color='#20B2AA', alpha=0.8)

        ax4.set_title('富营养化指数分布', fontsize=14, fontweight='bold')
        ax4.set_xlabel('富营养化指数', fontsize=12)
        ax4.set_ylabel('记录数量', fontsize=12)
        ax4.grid(True, alpha=0.3, axis='y')

        plt.tight_layout()
        filename = f'问题1_污染状况综合分析_{self.timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        self.logger.info(f"污染状况分析图已保存：{filename}")
        plt.close()
        return filename

    # ==================== 三维可视化方法 ====================

    def create_3d_spatiotemporal_chart(self, df: pd.DataFrame) -> str:
        """创建时空三维分析图"""
        fig = plt.figure(figsize=(16, 12))

        # 3D散点图
        ax1 = fig.add_subplot(221, projection='3d')

        # 准备数据
        x = df['station_code']  # 站点编码
        y = df['day_of_year']   # 年内天数
        z = df['wqi']           # WQI值
        colors = df['wqi']      # 颜色映射

        scatter = ax1.scatter(x, y, z, c=colors, cmap='RdYlGn', s=50, alpha=0.6)

        ax1.set_xlabel('监测站点编码', fontsize=10)
        ax1.set_ylabel('年内天数', fontsize=10)
        ax1.set_zlabel('WQI值', fontsize=10)
        ax1.set_title('时空WQI分布三维散点图', fontsize=12, fontweight='bold')

        # 添加颜色条
        plt.colorbar(scatter, ax=ax1, shrink=0.5, aspect=20, label='WQI值')

        # 3D表面图 - 污染物浓度
        ax2 = fig.add_subplot(222, projection='3d')

        # 创建网格数据
        stations_unique = sorted(df['station_code'].unique())
        months_unique = sorted(df['month'].unique())

        # 计算平均污染指数
        pivot_data = df.groupby(['station_code', 'month'])['comprehensive_pi'].mean().unstack(fill_value=0)

        X, Y = np.meshgrid(months_unique, stations_unique)
        Z = pivot_data.values

        surface = ax2.plot_surface(X, Y, Z, cmap='coolwarm', alpha=0.8)

        ax2.set_xlabel('月份', fontsize=10)
        ax2.set_ylabel('监测站点编码', fontsize=10)
        ax2.set_zlabel('综合污染指数', fontsize=10)
        ax2.set_title('污染指数时空分布表面图', fontsize=12, fontweight='bold')

        plt.colorbar(surface, ax=ax2, shrink=0.5, aspect=20, label='污染指数')

        # 3D柱状图 - 各站点各季节WQI
        ax3 = fig.add_subplot(223, projection='3d')

        seasonal_station_wqi = df.groupby(['station_code', 'season'])['wqi'].mean().unstack(fill_value=0)

        xpos, ypos = np.meshgrid(range(len(seasonal_station_wqi.columns)),
                                range(len(seasonal_station_wqi.index)))
        xpos = xpos.flatten()
        ypos = ypos.flatten()
        zpos = np.zeros_like(xpos)

        dx = dy = 0.8
        dz = seasonal_station_wqi.values.flatten()

        colors_3d = plt.cm.viridis(dz / dz.max())

        ax3.bar3d(xpos, ypos, zpos, dx, dy, dz, color=colors_3d, alpha=0.8)

        ax3.set_xlabel('季节', fontsize=10)
        ax3.set_ylabel('监测站点', fontsize=10)
        ax3.set_zlabel('平均WQI', fontsize=10)
        ax3.set_title('站点-季节WQI三维柱状图', fontsize=12, fontweight='bold')

        # 相关性3D图
        ax4 = fig.add_subplot(224, projection='3d')

        # 选择主要污染物进行3D相关性分析
        x_corr = df['do']
        y_corr = df['codmn']
        z_corr = df['nh3n']
        colors_corr = df['wqi']

        scatter_corr = ax4.scatter(x_corr, y_corr, z_corr, c=colors_corr,
                                  cmap='RdYlGn', s=30, alpha=0.6)

        ax4.set_xlabel('溶解氧 (mg/L)', fontsize=10)
        ax4.set_ylabel('高锰酸盐指数 (mg/L)', fontsize=10)
        ax4.set_zlabel('氨氮 (mg/L)', fontsize=10)
        ax4.set_title('污染物三维相关性分析', fontsize=12, fontweight='bold')

        plt.colorbar(scatter_corr, ax=ax4, shrink=0.5, aspect=20, label='WQI值')

        plt.tight_layout()
        filename = f'问题1_三维时空分析_{self.timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        self.logger.info(f"三维时空分析图已保存：{filename}")
        plt.close()
        return filename

    # ==================== 敏感度分析方法 ====================

    def create_weight_sensitivity_analysis(self, df: pd.DataFrame) -> str:
        """创建权重敏感度分析图"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # 原始权重
        original_weights = self.weights.copy()
        original_wqi = df['wqi'].mean()

        # 权重变化范围
        weight_changes = np.arange(0.1, 0.6, 0.05)

        # 1. DO权重敏感度
        do_sensitivity = []
        for w in weight_changes:
            temp_weights = original_weights.copy()
            temp_weights['do'] = w
            # 重新归一化
            total = sum(temp_weights.values())
            temp_weights = {k: v/total for k, v in temp_weights.items()}

            # 重新计算WQI
            temp_wqi = self._calculate_wqi_with_weights(df, temp_weights)
            do_sensitivity.append(temp_wqi.mean())

        ax1.plot(weight_changes, do_sensitivity, marker='o', linewidth=2, color='#2E86AB')
        ax1.axhline(y=original_wqi, color='red', linestyle='--', alpha=0.7, label='原始WQI')
        ax1.axvline(x=original_weights['do'], color='red', linestyle='--', alpha=0.7, label='原始权重')
        ax1.set_title('溶解氧权重敏感度分析', fontsize=12, fontweight='bold')
        ax1.set_xlabel('DO权重', fontsize=10)
        ax1.set_ylabel('平均WQI', fontsize=10)
        ax1.grid(True, alpha=0.3)
        ax1.legend()

        # 2. CODMn权重敏感度
        codmn_sensitivity = []
        for w in weight_changes:
            temp_weights = original_weights.copy()
            temp_weights['codmn'] = w
            total = sum(temp_weights.values())
            temp_weights = {k: v/total for k, v in temp_weights.items()}

            temp_wqi = self._calculate_wqi_with_weights(df, temp_weights)
            codmn_sensitivity.append(temp_wqi.mean())

        ax2.plot(weight_changes, codmn_sensitivity, marker='s', linewidth=2, color='#A23B72')
        ax2.axhline(y=original_wqi, color='red', linestyle='--', alpha=0.7, label='原始WQI')
        ax2.axvline(x=original_weights['codmn'], color='red', linestyle='--', alpha=0.7, label='原始权重')
        ax2.set_title('高锰酸盐指数权重敏感度分析', fontsize=12, fontweight='bold')
        ax2.set_xlabel('CODMn权重', fontsize=10)
        ax2.set_ylabel('平均WQI', fontsize=10)
        ax2.grid(True, alpha=0.3)
        ax2.legend()

        # 3. NH3-N权重敏感度
        nh3n_sensitivity = []
        for w in weight_changes:
            temp_weights = original_weights.copy()
            temp_weights['nh3n'] = w
            total = sum(temp_weights.values())
            temp_weights = {k: v/total for k, v in temp_weights.items()}

            temp_wqi = self._calculate_wqi_with_weights(df, temp_weights)
            nh3n_sensitivity.append(temp_wqi.mean())

        ax3.plot(weight_changes, nh3n_sensitivity, marker='^', linewidth=2, color='#F18F01')
        ax3.axhline(y=original_wqi, color='red', linestyle='--', alpha=0.7, label='原始WQI')
        ax3.axvline(x=original_weights['nh3n'], color='red', linestyle='--', alpha=0.7, label='原始权重')
        ax3.set_title('氨氮权重敏感度分析', fontsize=12, fontweight='bold')
        ax3.set_xlabel('NH3-N权重', fontsize=10)
        ax3.set_ylabel('平均WQI', fontsize=10)
        ax3.grid(True, alpha=0.3)
        ax3.legend()

        # 4. 综合敏感度对比
        ax4.plot(weight_changes, do_sensitivity, marker='o', label='DO权重', linewidth=2)
        ax4.plot(weight_changes, codmn_sensitivity, marker='s', label='CODMn权重', linewidth=2)
        ax4.plot(weight_changes, nh3n_sensitivity, marker='^', label='NH3-N权重', linewidth=2)
        ax4.axhline(y=original_wqi, color='red', linestyle='--', alpha=0.7, label='原始WQI')

        ax4.set_title('权重敏感度综合对比', fontsize=12, fontweight='bold')
        ax4.set_xlabel('权重值', fontsize=10)
        ax4.set_ylabel('平均WQI', fontsize=10)
        ax4.grid(True, alpha=0.3)
        ax4.legend()

        plt.tight_layout()
        filename = f'问题1_权重敏感度分析_{self.timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        self.logger.info(f"权重敏感度分析图已保存：{filename}")
        plt.close()
        return filename

    def _calculate_wqi_with_weights(self, df: pd.DataFrame, weights: Dict) -> pd.Series:
        """使用指定权重计算WQI"""
        # 重新计算污染指数
        do_pi = 5.0 / df['do']
        codmn_pi = df['codmn'] / 6.0
        nh3n_pi = df['nh3n'] / 1.0
        ph_pi = np.where(
            (df['ph'] >= 6.0) & (df['ph'] <= 9.0), 0,
            np.maximum(np.abs(df['ph'] - 6.0), np.abs(df['ph'] - 9.0))
        )

        # 计算综合污染指数
        comprehensive_pi = (
            do_pi * weights['do'] +
            codmn_pi * weights['codmn'] +
            nh3n_pi * weights['nh3n'] +
            ph_pi * weights['ph']
        )

        # 转换为WQI（简化版本）
        wqi = 100 - comprehensive_pi * 20
        wqi = np.clip(wqi, 0, 100)

        return wqi

    # ==================== 统计分析方法 ====================

    def create_correlation_heatmap(self, df: pd.DataFrame) -> str:
        """创建相关性热力图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

        # 1. 水质指标相关性
        water_params = ['ph', 'do', 'codmn', 'nh3n', 'wqi', 'comprehensive_pi', 'ecological_risk']
        corr_matrix1 = df[water_params].corr()

        sns.heatmap(corr_matrix1, annot=True, cmap='RdBu_r', center=0,
                   square=True, ax=ax1, cbar_kws={'label': '相关系数'})
        ax1.set_title('水质指标相关性分析', fontsize=14, fontweight='bold')

        # 2. 污染指数相关性
        pollution_params = ['do_pi', 'codmn_pi', 'nh3n_pi', 'comprehensive_pi',
                           'ecological_risk', 'eutrophication_index']
        corr_matrix2 = df[pollution_params].corr()

        sns.heatmap(corr_matrix2, annot=True, cmap='RdBu_r', center=0,
                   square=True, ax=ax2, cbar_kws={'label': '相关系数'})
        ax2.set_title('污染指数相关性分析', fontsize=14, fontweight='bold')

        plt.tight_layout()
        filename = f'问题1_相关性热力图_{self.timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        self.logger.info(f"相关性热力图已保存：{filename}")
        plt.close()
        return filename

    def create_pca_analysis(self, df: pd.DataFrame) -> str:
        """创建主成分分析图"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # 准备数据
        features = ['ph', 'do', 'codmn', 'nh3n']
        X = df[features].values

        # 标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # PCA分析
        pca = PCA()
        X_pca = pca.fit_transform(X_scaled)

        # 1. 主成分贡献率
        explained_variance = pca.explained_variance_ratio_
        cumulative_variance = np.cumsum(explained_variance)

        ax1.bar(range(1, len(explained_variance) + 1), explained_variance,
               alpha=0.8, color='skyblue', label='单个主成分')
        ax1.plot(range(1, len(cumulative_variance) + 1), cumulative_variance,
                'ro-', label='累积贡献率')
        ax1.set_title('主成分贡献率分析', fontsize=12, fontweight='bold')
        ax1.set_xlabel('主成分', fontsize=10)
        ax1.set_ylabel('贡献率', fontsize=10)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 前两个主成分散点图
        colors = df['wqi']
        scatter = ax2.scatter(X_pca[:, 0], X_pca[:, 1], c=colors, cmap='RdYlGn', alpha=0.6)
        ax2.set_title('前两个主成分散点图', fontsize=12, fontweight='bold')
        ax2.set_xlabel(f'PC1 ({explained_variance[0]:.1%})', fontsize=10)
        ax2.set_ylabel(f'PC2 ({explained_variance[1]:.1%})', fontsize=10)
        plt.colorbar(scatter, ax=ax2, label='WQI值')

        # 3. 载荷图
        loadings = pca.components_[:2].T
        for i, feature in enumerate(features):
            ax3.arrow(0, 0, loadings[i, 0], loadings[i, 1],
                     head_width=0.05, head_length=0.05, fc='red', ec='red')
            ax3.text(loadings[i, 0]*1.1, loadings[i, 1]*1.1, feature,
                    fontsize=10, ha='center', va='center')

        ax3.set_xlim(-1, 1)
        ax3.set_ylim(-1, 1)
        ax3.set_title('主成分载荷图', fontsize=12, fontweight='bold')
        ax3.set_xlabel(f'PC1 ({explained_variance[0]:.1%})', fontsize=10)
        ax3.set_ylabel(f'PC2 ({explained_variance[1]:.1%})', fontsize=10)
        ax3.grid(True, alpha=0.3)
        ax3.axhline(y=0, color='k', linestyle='-', alpha=0.3)
        ax3.axvline(x=0, color='k', linestyle='-', alpha=0.3)

        # 4. 各站点在主成分空间的分布
        station_means = df.groupby('station')[['ph', 'do', 'codmn', 'nh3n']].mean()
        station_scaled = scaler.transform(station_means.values)
        station_pca = pca.transform(station_scaled)

        ax4.scatter(station_pca[:, 0], station_pca[:, 1], s=100, alpha=0.8)
        for i, station in enumerate(station_means.index):
            ax4.annotate(station[:8], (station_pca[i, 0], station_pca[i, 1]),
                        fontsize=8, ha='center')

        ax4.set_title('各站点主成分分布', fontsize=12, fontweight='bold')
        ax4.set_xlabel(f'PC1 ({explained_variance[0]:.1%})', fontsize=10)
        ax4.set_ylabel(f'PC2 ({explained_variance[1]:.1%})', fontsize=10)
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()
        filename = f'问题1_主成分分析_{self.timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        self.logger.info(f"主成分分析图已保存：{filename}")
        plt.close()
        return filename

    # ==================== 空间和时间分析方法 ====================

    def analyze_spatial_distribution(self, df: pd.DataFrame) -> Dict:
        """空间分布分析（增强版）"""
        spatial_results = {}

        # 各站点基本统计
        station_stats = df.groupby('station').agg({
            'wqi': ['mean', 'std', 'min', 'max'],
            'comprehensive_pi': ['mean', 'std'],
            'ecological_risk': ['mean', 'std'],
            'stability_index': 'first',
            'eutrophication_index': 'mean',
            'calculated_class': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else 'III'
        }).round(2)

        # 污染程度统计
        pollution_counts = df.groupby('station')['pollution_level'].value_counts().unstack(fill_value=0)
        pollution_rates = pollution_counts.div(pollution_counts.sum(axis=1), axis=0) * 100

        # 水质类别分布
        class_distribution = df.groupby('station')['calculated_class'].value_counts().unstack(fill_value=0)
        class_percentage = class_distribution.div(class_distribution.sum(axis=1), axis=0) * 100

        # 主要污染物识别
        pollutant_stats = df.groupby('station').agg({
            'do_pi': 'mean',
            'codmn_pi': 'mean',
            'nh3n_pi': 'mean',
            'ph_pi': 'mean'
        }).round(3)

        # 生态风险评估
        risk_stats = df.groupby('station').agg({
            'ecological_risk': ['mean', 'max'],
            'risk_level': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else '中等风险'
        })

        spatial_results = {
            'station_stats': station_stats,
            'pollution_rates': pollution_rates,
            'class_distribution': class_distribution,
            'class_percentage': class_percentage,
            'pollutant_stats': pollutant_stats,
            'risk_stats': risk_stats
        }

        self.logger.info("增强空间分析完成")
        return spatial_results

    def analyze_temporal_trends(self, df: pd.DataFrame) -> Dict:
        """时间趋势分析（增强版）"""
        temporal_results = {}

        # 月度统计
        monthly_wqi = df.groupby('year_month')['wqi'].mean()
        monthly_stats = df.groupby('year_month').agg({
            'wqi': ['mean', 'std'],
            'comprehensive_pi': 'mean',
            'ecological_risk': 'mean',
            'eutrophication_index': 'mean'
        }).round(2)

        # 季节性分析
        seasonal_stats = df.groupby('season').agg({
            'wqi': ['mean', 'std'],
            'comprehensive_pi': 'mean',
            'ecological_risk': 'mean',
            'do': 'mean',
            'codmn': 'mean',
            'nh3n': 'mean'
        }).round(2)

        # 年度趋势
        yearly_stats = df.groupby('year').agg({
            'wqi': ['mean', 'std'],
            'comprehensive_pi': 'mean',
            'ecological_risk': 'mean'
        }).round(2)

        # 趋势分析
        if len(monthly_wqi) > 1:
            trend_slope = np.polyfit(range(len(monthly_wqi)), monthly_wqi.values, 1)[0]
            trend_direction = '改善' if trend_slope > 0 else '恶化' if trend_slope < 0 else '稳定'
        else:
            trend_slope = 0
            trend_direction = '稳定'

        # 变异性分析
        wqi_cv = df['wqi'].std() / df['wqi'].mean()
        stability_assessment = '稳定' if wqi_cv < 0.2 else '中等变化' if wqi_cv < 0.4 else '高变化'

        temporal_results = {
            'monthly_wqi': monthly_wqi,
            'monthly_stats': monthly_stats,
            'seasonal_stats': seasonal_stats,
            'yearly_stats': yearly_stats,
            'trend_slope': trend_slope,
            'trend_direction': trend_direction,
            'wqi_cv': wqi_cv,
            'stability_assessment': stability_assessment
        }

        self.logger.info("增强时间分析完成")
        return temporal_results

    # ==================== 主运行方法 ====================

    def run_comprehensive_analysis(self, data_file: str = 'yangtze_complete_data_2003_2005.csv') -> Dict[str, str]:
        """
        运行完整的综合分析

        Args:
            data_file: 数据文件路径

        Returns:
            Dict[str, str]: 生成的图表文件名字典
        """
        self.logger.info("开始长江水质综合评价分析（增强版）")

        # 1. 数据加载和处理
        df = self.load_and_process_data(data_file)

        # 2. 空间和时间分析
        spatial_results = self.analyze_spatial_distribution(df)
        temporal_results = self.analyze_temporal_trends(df)

        # 3. 生成所有独立图表
        generated_files = {}

        try:
            # 基础分析图表
            self.logger.info("生成基础分析图表...")
            generated_files['wqi_trend'] = self.create_wqi_trend_chart(df, temporal_results)
            generated_files['station_comparison'] = self.create_station_comparison_chart(df, spatial_results)
            generated_files['water_class_distribution'] = self.create_water_class_distribution_chart(df)
            generated_files['seasonal_analysis'] = self.create_seasonal_analysis_chart(df, temporal_results)
            generated_files['pollution_analysis'] = self.create_pollution_analysis_chart(df, spatial_results)

            # 三维可视化
            self.logger.info("生成三维可视化图表...")
            generated_files['3d_spatiotemporal'] = self.create_3d_spatiotemporal_chart(df)

            # 敏感度分析
            self.logger.info("生成敏感度分析图表...")
            generated_files['weight_sensitivity'] = self.create_weight_sensitivity_analysis(df)

            # 统计分析
            self.logger.info("生成统计分析图表...")
            generated_files['correlation_heatmap'] = self.create_correlation_heatmap(df)
            generated_files['pca_analysis'] = self.create_pca_analysis(df)

        except Exception as e:
            self.logger.error(f"图表生成过程中出现错误：{e}")
            raise

        # 4. 生成综合评价报告
        self.generate_comprehensive_report(df, spatial_results, temporal_results, generated_files)

        self.logger.info(f"分析完成！共生成 {len(generated_files)} 个图表文件")
        return generated_files

    def generate_comprehensive_report(self, df: pd.DataFrame, spatial_results: Dict,
                                    temporal_results: Dict, generated_files: Dict):
        """生成综合评价报告"""
        report_filename = f'问题1_综合评价报告_{self.timestamp}.txt'

        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("长江水质综合评价分析报告（增强版）\n")
            f.write("=" * 80 + "\n\n")

            # 数据概况
            f.write("1. 数据概况\n")
            f.write("-" * 40 + "\n")
            f.write(f"分析时间范围：{df['date'].min()} 至 {df['date'].max()}\n")
            f.write(f"监测站点数量：{df['station'].nunique()} 个\n")
            f.write(f"监测记录总数：{len(df)} 条\n")
            f.write(f"数据完整性：{(1 - df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100:.1f}%\n\n")

            # 水质总体评价
            f.write("2. 水质总体评价\n")
            f.write("-" * 40 + "\n")
            avg_wqi = df['wqi'].mean()
            f.write(f"平均水质综合指数(WQI)：{avg_wqi:.1f}\n")
            f.write(f"总体水质等级：{self._get_pollution_level(avg_wqi)}\n")
            f.write(f"水质稳定性评估：{temporal_results['stability_assessment']}\n")
            f.write(f"水质变化趋势：{temporal_results['trend_direction']}\n\n")

            # 水质类别分布
            f.write("3. 水质类别分布\n")
            f.write("-" * 40 + "\n")
            class_counts = df['calculated_class'].value_counts()
            for cls, count in class_counts.items():
                percentage = count / len(df) * 100
                f.write(f"{cls}类水质：{count} 条记录 ({percentage:.1f}%)\n")

            drinkable_rate = class_counts[class_counts.index.isin(['I', 'II', 'III'])].sum() / len(df) * 100
            f.write(f"\n可饮用水比例（I-III类）：{drinkable_rate:.1f}%\n")
            f.write(f"污染水比例（IV-劣V类）：{100 - drinkable_rate:.1f}%\n\n")

            # 空间分布特征
            f.write("4. 空间分布特征\n")
            f.write("-" * 40 + "\n")
            station_wqi = spatial_results['station_stats']['wqi']['mean']
            best_station = station_wqi.idxmax()
            worst_station = station_wqi.idxmin()
            f.write(f"水质最佳站点：{best_station} (WQI={station_wqi[best_station]:.1f})\n")
            f.write(f"水质最差站点：{worst_station} (WQI={station_wqi[worst_station]:.1f})\n")
            f.write(f"站点间WQI差异：{station_wqi.max() - station_wqi.min():.1f}\n\n")

            # 时间变化特征
            f.write("5. 时间变化特征\n")
            f.write("-" * 40 + "\n")
            seasonal_wqi = temporal_results['seasonal_stats']['wqi']['mean']
            best_season = seasonal_wqi.idxmax()
            worst_season = seasonal_wqi.idxmin()
            f.write(f"水质最佳季节：{best_season} (WQI={seasonal_wqi[best_season]:.1f})\n")
            f.write(f"水质最差季节：{worst_season} (WQI={seasonal_wqi[worst_season]:.1f})\n")
            f.write(f"季节性变化幅度：{seasonal_wqi.max() - seasonal_wqi.min():.1f}\n\n")

            # 污染状况分析
            f.write("6. 污染状况分析\n")
            f.write("-" * 40 + "\n")
            avg_comprehensive_pi = df['comprehensive_pi'].mean()
            avg_ecological_risk = df['ecological_risk'].mean()
            avg_eutrophication = df['eutrophication_index'].mean()

            f.write(f"平均综合污染指数：{avg_comprehensive_pi:.2f}\n")
            f.write(f"平均生态风险指数：{avg_ecological_risk:.2f}\n")
            f.write(f"平均富营养化指数：{avg_eutrophication:.2f}\n\n")

            # 主要污染物
            pollutant_means = df[['do_pi', 'codmn_pi', 'nh3n_pi']].mean()
            max_pollutant = pollutant_means.idxmax()
            pollutant_names = {'do_pi': '溶解氧', 'codmn_pi': '高锰酸盐指数', 'nh3n_pi': '氨氮'}
            f.write(f"主要污染因子：{pollutant_names[max_pollutant]} (污染指数={pollutant_means[max_pollutant]:.2f})\n\n")

            # 生成的图表文件
            f.write("7. 生成的分析图表\n")
            f.write("-" * 40 + "\n")
            for chart_type, filename in generated_files.items():
                f.write(f"{chart_type}: {filename}\n")

            f.write("\n" + "=" * 80 + "\n")
            f.write(f"报告生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("分析系统：长江水质综合评价分析器增强版\n")
            f.write("=" * 80 + "\n")

        self.logger.info(f"综合评价报告已保存：{report_filename}")


def main():
    """主函数"""
    try:
        # 创建分析器实例
        analyzer = YangtzeEnhancedWaterQualityAnalyzer()

        # 运行完整分析
        generated_files = analyzer.run_comprehensive_analysis()

        print("\n" + "="*60)
        print("长江水质综合评价分析完成！")
        print("="*60)
        print(f"共生成 {len(generated_files)} 个分析图表：")
        for chart_type, filename in generated_files.items():
            print(f"  - {chart_type}: {filename}")
        print("="*60)

    except Exception as e:
        print(f"分析过程中出现错误：{e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
