#!/usr/bin/env python3
"""
简单测试版本 - 诊断问题
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import logging
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def setup_logging():
    logging.basicConfig(level=logging.INFO, 
                      format='%(asctime)s - %(levelname)s - %(message)s')
    return logging.getLogger('simple_test')

def classify_water_quality(row):
    """水质分类"""
    ph, do, codmn, nh3n = row['ph'], row['do'], row['codmn'], row['nh3n']
    
    if ph < 6.0 or ph > 9.0:
        return '劣V'
    if do < 2.0 or codmn > 15.0 or nh3n > 2.0:
        return '劣V'
    elif do < 3.0 or codmn > 10.0 or nh3n > 1.5:
        return 'V'
    elif do < 5.0 or codmn > 6.0 or nh3n > 1.0:
        return 'IV'
    elif do < 6.0 or codmn > 4.0 or nh3n > 0.5:
        return 'III'
    elif do < 7.5 or codmn > 2.0 or nh3n > 0.15:
        return 'II'
    else:
        return 'I'

def main():
    logger = setup_logging()
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    try:
        logger.info("开始简单测试...")
        
        # 1. 加载数据
        logger.info("加载数据...")
        df = pd.read_csv('yangtze_complete_data_2003_2005.csv')
        logger.info(f"原始数据：{len(df)}条记录")
        
        # 2. 数据处理
        logger.info("处理数据...")
        df['date'] = pd.to_datetime(df['date'])
        df = df.dropna(subset=['ph', 'do', 'codmn', 'nh3n'])
        logger.info(f"清洗后数据：{len(df)}条记录")
        
        # 3. 水质分类
        logger.info("进行水质分类...")
        df['calculated_class'] = df.apply(classify_water_quality, axis=1)
        
        # 4. WQI计算
        logger.info("计算WQI...")
        class_scores = {'I': 100, 'II': 80, 'III': 60, 'IV': 40, 'V': 20, '劣V': 0}
        df['wqi'] = df['calculated_class'].map(class_scores)
        
        # 5. 时间字段处理
        logger.info("处理时间字段...")
        df['year'] = df['date'].dt.year
        df['month'] = df['date'].dt.month
        df['year_month_str'] = df['date'].dt.strftime('%Y-%m')
        
        # 6. 计算月度WQI
        logger.info("计算月度WQI...")
        monthly_wqi = df.groupby('year_month_str')['wqi'].mean()
        logger.info(f"月度WQI计算完成，共{len(monthly_wqi)}个月")
        
        # 7. 创建简单图表
        logger.info("创建WQI趋势图...")
        fig, ax = plt.subplots(figsize=(12, 6))
        
        x_range = range(len(monthly_wqi))
        ax.plot(x_range, monthly_wqi.values, marker='o', linewidth=2, markersize=4)
        
        ax.set_title('长江水质WQI时间趋势', fontsize=14, fontweight='bold')
        ax.set_xlabel('时间序列', fontsize=12)
        ax.set_ylabel('WQI值', fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # 添加参考线
        ax.axhline(y=80, color='green', linestyle='--', alpha=0.7, label='优秀水质')
        ax.axhline(y=60, color='orange', linestyle='--', alpha=0.7, label='良好水质')
        ax.axhline(y=40, color='red', linestyle='--', alpha=0.7, label='污染水质')
        ax.legend()
        
        plt.tight_layout()
        filename1 = f'问题1_简单WQI趋势_{timestamp}.png'
        plt.savefig(filename1, dpi=300, bbox_inches='tight')
        plt.close()
        logger.info(f"WQI趋势图已保存：{filename1}")
        
        # 8. 创建站点对比图
        logger.info("创建站点对比图...")
        fig, ax = plt.subplots(figsize=(12, 8))
        
        station_wqi = df.groupby('station')['wqi'].mean().sort_values()
        colors = ['red' if x < 40 else 'orange' if x < 60 else 'yellow' if x < 80 else 'green'
                 for x in station_wqi.values]
        
        bars = ax.barh(range(len(station_wqi)), station_wqi.values, color=colors, alpha=0.7)
        ax.set_yticks(range(len(station_wqi)))
        ax.set_yticklabels([s[:10] + '...' if len(s) > 10 else s for s in station_wqi.index], fontsize=9)
        
        ax.set_title('各监测站点WQI对比', fontsize=14, fontweight='bold')
        ax.set_xlabel('WQI值', fontsize=12)
        ax.grid(True, alpha=0.3, axis='x')
        
        plt.tight_layout()
        filename2 = f'问题1_简单站点对比_{timestamp}.png'
        plt.savefig(filename2, dpi=300, bbox_inches='tight')
        plt.close()
        logger.info(f"站点对比图已保存：{filename2}")
        
        # 9. 创建水质类别分布图
        logger.info("创建水质类别分布图...")
        fig, ax = plt.subplots(figsize=(10, 6))
        
        class_counts = df['calculated_class'].value_counts()
        colors_pie = ['green', 'lightgreen', 'yellow', 'orange', 'red', 'darkred']
        
        ax.pie(class_counts.values, labels=class_counts.index, 
               colors=colors_pie[:len(class_counts)], autopct='%1.1f%%', startangle=90)
        ax.set_title('水质类别分布', fontsize=14, fontweight='bold')
        
        plt.tight_layout()
        filename3 = f'问题1_简单类别分布_{timestamp}.png'
        plt.savefig(filename3, dpi=300, bbox_inches='tight')
        plt.close()
        logger.info(f"类别分布图已保存：{filename3}")
        
        # 10. 生成简单报告
        logger.info("生成简单报告...")
        report_filename = f'问题1_简单分析报告_{timestamp}.txt'
        
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("长江水质简单分析报告\n")
            f.write("=" * 40 + "\n\n")
            
            f.write(f"数据概况：\n")
            f.write(f"- 分析时间范围：{df['date'].min()} 至 {df['date'].max()}\n")
            f.write(f"- 监测站点数量：{df['station'].nunique()} 个\n")
            f.write(f"- 监测记录总数：{len(df)} 条\n\n")
            
            f.write(f"水质总体评价：\n")
            avg_wqi = df['wqi'].mean()
            f.write(f"- 平均WQI：{avg_wqi:.1f}\n")
            
            f.write(f"\n水质类别分布：\n")
            for cls, count in class_counts.items():
                percentage = count / len(df) * 100
                f.write(f"- {cls}类：{count} 条记录 ({percentage:.1f}%)\n")
            
            f.write(f"\n生成的图表文件：\n")
            f.write(f"- {filename1}\n")
            f.write(f"- {filename2}\n")
            f.write(f"- {filename3}\n")
        
        logger.info(f"简单报告已保存：{report_filename}")
        
        print("\n" + "="*50)
        print("简单测试完成！")
        print("="*50)
        print("生成的文件：")
        print(f"  - {filename1}")
        print(f"  - {filename2}")
        print(f"  - {filename3}")
        print(f"  - {report_filename}")
        print("="*50)
        
    except Exception as e:
        logger.error(f"简单测试失败：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
