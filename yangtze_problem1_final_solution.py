#!/usr/bin/env python3
"""
长江水质综合评价分析系统 - 问题1最终解决方案
实现用户要求的所有功能：
1. 每张图表独立保存（一张图一个文件）
2. 优化评价体系（多指标综合评价）
3. 敏感度分析（权重敏感度分析）
4. 统计分析（相关性分析、热力图）
5. 文件命名：问题1+功能描述+时间戳
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
import logging
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def setup_logging():
    logging.basicConfig(level=logging.INFO, 
                      format='%(asctime)s - %(levelname)s - %(message)s')
    return logging.getLogger('problem1_final')

def classify_water_quality(row):
    """根据GB3838-2002标准分类水质"""
    ph, do, codmn, nh3n = row['ph'], row['do'], row['codmn'], row['nh3n']
    
    if ph < 6.0 or ph > 9.0:
        return '劣V'
    if do < 2.0 or codmn > 15.0 or nh3n > 2.0:
        return '劣V'
    elif do < 3.0 or codmn > 10.0 or nh3n > 1.5:
        return 'V'
    elif do < 5.0 or codmn > 6.0 or nh3n > 1.0:
        return 'IV'
    elif do < 6.0 or codmn > 4.0 or nh3n > 0.5:
        return 'III'
    elif do < 7.5 or codmn > 2.0 or nh3n > 0.15:
        return 'II'
    else:
        return 'I'

def calculate_enhanced_indices(df):
    """计算增强评价指标体系"""
    df = df.copy()
    
    # 1. 基础WQI计算（基于水质类别）
    class_scores = {'I': 100, 'II': 80, 'III': 60, 'IV': 40, 'V': 20, '劣V': 0}
    df['wqi'] = df['calculated_class'].map(class_scores)
    
    # 2. 污染指数计算
    df['do_pi'] = 5.0 / df['do']  # DO污染指数（值越大污染越重）
    df['codmn_pi'] = df['codmn'] / 6.0  # CODMn污染指数
    df['nh3n_pi'] = df['nh3n'] / 1.0  # NH3-N污染指数
    
    # 3. pH污染指数
    df['ph_pi'] = np.where(
        (df['ph'] >= 6.0) & (df['ph'] <= 9.0), 0,
        np.maximum(np.abs(df['ph'] - 6.0), np.abs(df['ph'] - 9.0))
    )
    
    # 4. 指标权重（基于环境影响重要性）
    weights = {'do': 0.25, 'codmn': 0.30, 'nh3n': 0.30, 'ph': 0.15}
    
    # 5. 综合污染指数
    df['comprehensive_pi'] = (
        df['do_pi'] * weights['do'] +
        df['codmn_pi'] * weights['codmn'] +
        df['nh3n_pi'] * weights['nh3n'] +
        df['ph_pi'] * weights['ph']
    )
    
    # 6. 生态风险评估指数
    df['ecological_risk'] = 0
    for idx, row in df.iterrows():
        risk = 0
        # DO风险评估
        if row['do'] < 3.0:
            risk += 3
        elif row['do'] < 5.0:
            risk += 2
        elif row['do'] < 6.0:
            risk += 1
        
        # CODMn风险评估
        if row['codmn'] > 10.0:
            risk += 3
        elif row['codmn'] > 6.0:
            risk += 2
        elif row['codmn'] > 4.0:
            risk += 1
        
        # NH3-N风险评估
        if row['nh3n'] > 1.5:
            risk += 3
        elif row['nh3n'] > 1.0:
            risk += 2
        elif row['nh3n'] > 0.5:
            risk += 1
        
        df.at[idx, 'ecological_risk'] = risk
    
    # 7. 富营养化指数
    df['eutrophication_index'] = 0
    for idx, row in df.iterrows():
        score = 0
        # 基于NH3-N的富营养化评估
        if row['nh3n'] > 2.0:
            score += 4
        elif row['nh3n'] > 1.0:
            score += 3
        elif row['nh3n'] > 0.5:
            score += 2
        elif row['nh3n'] > 0.15:
            score += 1
        
        # 基于CODMn的富营养化评估
        if row['codmn'] > 10.0:
            score += 3
        elif row['codmn'] > 6.0:
            score += 2
        elif row['codmn'] > 4.0:
            score += 1
        
        df.at[idx, 'eutrophication_index'] = score
    
    # 8. 污染程度分级
    def get_pollution_level(wqi):
        if wqi >= 80:
            return '优秀'
        elif wqi >= 60:
            return '良好'
        elif wqi >= 40:
            return '轻度污染'
        elif wqi >= 20:
            return '中度污染'
        else:
            return '重度污染'
    
    df['pollution_level'] = df['wqi'].apply(get_pollution_level)
    
    return df

def create_wqi_trend_chart(df, timestamp):
    """图表1：WQI时间趋势分析（独立图表）"""
    plt.figure(figsize=(14, 8))
    
    monthly_wqi = df.groupby('year_month_str')['wqi'].mean()
    
    # 主趋势线
    plt.plot(range(len(monthly_wqi)), monthly_wqi.values, 
           marker='o', linewidth=3, markersize=6, color='#2E86AB', 
           label='月度WQI', alpha=0.8)
    
    # 添加趋势线
    if len(monthly_wqi) > 1:
        x_numeric = range(len(monthly_wqi))
        z = np.polyfit(x_numeric, monthly_wqi.values, 1)
        p = np.poly1d(z)
        trend_direction = '改善' if z[0] > 0 else '恶化' if z[0] < 0 else '稳定'
        plt.plot(x_numeric, p(x_numeric), 
               "r--", alpha=0.8, linewidth=2, 
               label=f'趋势线: {trend_direction}')
    
    # 添加水质等级参考线
    plt.axhline(y=80, color='green', linestyle=':', alpha=0.7, label='优秀水质线(80)')
    plt.axhline(y=60, color='orange', linestyle=':', alpha=0.7, label='良好水质线(60)')
    plt.axhline(y=40, color='red', linestyle=':', alpha=0.7, label='污染水质线(40)')
    
    # 填充区域
    plt.fill_between(range(len(monthly_wqi)), monthly_wqi.values, alpha=0.3, color='#2E86AB')
    
    plt.title('长江水质综合指数(WQI)时间变化趋势分析', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('时间序列（月份）', fontsize=12)
    plt.ylabel('WQI值', fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.legend(fontsize=10)
    
    # 添加统计信息
    mean_wqi = monthly_wqi.mean()
    std_wqi = monthly_wqi.std()
    plt.text(0.02, 0.98, f'平均WQI: {mean_wqi:.1f}\n标准差: {std_wqi:.1f}', 
           transform=plt.gca().transAxes, fontsize=10, verticalalignment='top',
           bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    filename = f'问题1_WQI时间趋势分析_{timestamp}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()
    return filename

def create_station_comparison_chart(df, timestamp):
    """图表2：各站点WQI对比分析（独立图表）"""
    plt.figure(figsize=(14, 10))
    
    station_wqi = df.groupby('station')['wqi'].mean().sort_values()
    colors = ['#d62728' if x < 40 else '#ff7f0e' if x < 60 else '#ffbb78' if x < 80 else '#2ca02c' 
             for x in station_wqi.values]
    
    bars = plt.barh(range(len(station_wqi)), station_wqi.values, color=colors, alpha=0.8)
    
    # 设置y轴标签
    plt.yticks(range(len(station_wqi)), 
              [s[:12] + '...' if len(s) > 12 else s for s in station_wqi.index], 
              fontsize=10)
    
    # 添加数值标签
    for i, (bar, value) in enumerate(zip(bars, station_wqi.values)):
        plt.text(value + 1, i, f'{value:.1f}', 
               va='center', fontsize=9, fontweight='bold')
    
    # 添加参考线
    plt.axvline(x=80, color='green', linestyle='--', alpha=0.7, label='优秀水质线')
    plt.axvline(x=60, color='orange', linestyle='--', alpha=0.7, label='良好水质线')
    plt.axvline(x=40, color='red', linestyle='--', alpha=0.7, label='污染水质线')
    
    plt.title('长江各监测站点水质综合指数(WQI)对比分析', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('WQI值', fontsize=12)
    plt.ylabel('监测站点', fontsize=12)
    plt.grid(True, alpha=0.3, axis='x')
    plt.legend(fontsize=10)
    
    # 添加统计信息
    best_station = station_wqi.idxmax()
    worst_station = station_wqi.idxmin()
    plt.text(0.02, 0.98, f'最佳站点: {best_station[:15]}... ({station_wqi.max():.1f})\n最差站点: {worst_station[:15]}... ({station_wqi.min():.1f})', 
           transform=plt.gca().transAxes, fontsize=10, verticalalignment='top',
           bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    plt.tight_layout()
    filename = f'问题1_各站点WQI对比分析_{timestamp}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()
    return filename

def create_water_class_distribution_chart(df, timestamp):
    """图表3：水质类别分布分析（独立图表）"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

    # 左图：饼图
    class_counts = df['calculated_class'].value_counts()
    colors_pie = ['#2ca02c', '#98df8a', '#ffbb78', '#ff7f0e', '#d62728', '#8c564b']

    ax1.pie(class_counts.values,
           labels=class_counts.index,
           colors=colors_pie[:len(class_counts)],
           autopct='%1.1f%%',
           startangle=90,
           explode=[0.05 if cls in ['劣V', 'V'] else 0 for cls in class_counts.index])

    ax1.set_title('水质类别分布比例', fontsize=14, fontweight='bold')

    # 右图：柱状图
    bars = ax2.bar(class_counts.index, class_counts.values,
                  color=colors_pie[:len(class_counts)], alpha=0.8)

    # 添加数值标签
    for bar, value in zip(bars, class_counts.values):
        ax2.text(bar.get_x() + bar.get_width()/2, value + 2,
                f'{value}\n({value/len(df)*100:.1f}%)',
                ha='center', va='bottom', fontsize=10, fontweight='bold')

    ax2.set_title('水质类别记录数量统计', fontsize=14, fontweight='bold')
    ax2.set_xlabel('水质类别', fontsize=12)
    ax2.set_ylabel('记录数量', fontsize=12)
    ax2.grid(True, alpha=0.3, axis='y')

    # 添加总体评价
    drinkable_rate = class_counts[class_counts.index.isin(['I', 'II', 'III'])].sum() / len(df) * 100
    polluted_rate = class_counts[class_counts.index.isin(['IV', 'V', '劣V'])].sum() / len(df) * 100

    fig.suptitle(f'长江水质类别分布分析\n可饮用水比例: {drinkable_rate:.1f}% | 污染水比例: {polluted_rate:.1f}%',
                fontsize=16, fontweight='bold')

    plt.tight_layout()
    filename = f'问题1_水质类别分布分析_{timestamp}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()
    return filename

def create_seasonal_analysis_chart(df, timestamp):
    """图表4：季节性变化分析（独立图表）"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

    # 左图：季节性WQI变化
    seasonal_wqi = df.groupby('season')['wqi'].mean()
    colors_season = ['#87CEEB', '#98FB98', '#FFA500', '#D2691E']

    bars1 = ax1.bar(seasonal_wqi.index, seasonal_wqi.values,
                   color=colors_season, alpha=0.8, width=0.6)

    # 添加数值标签
    for bar, value in zip(bars1, seasonal_wqi.values):
        ax1.text(bar.get_x() + bar.get_width()/2, value + 1,
                f'{value:.1f}', ha='center', va='bottom',
                fontsize=12, fontweight='bold')

    ax1.set_title('季节性水质变化', fontsize=14, fontweight='bold')
    ax1.set_ylabel('平均WQI值', fontsize=12)
    ax1.grid(True, alpha=0.3, axis='y')

    # 右图：季节性污染物浓度变化
    seasonal_pollutants = df.groupby('season')[['do', 'codmn', 'nh3n']].mean()

    x = np.arange(len(seasonal_pollutants.index))
    width = 0.25

    ax2.bar(x - width, seasonal_pollutants['do'], width,
           label='溶解氧(DO)', alpha=0.8, color='#2E86AB')
    ax2.bar(x, seasonal_pollutants['codmn'], width,
           label='高锰酸盐指数', alpha=0.8, color='#A23B72')
    ax2.bar(x + width, seasonal_pollutants['nh3n'], width,
           label='氨氮', alpha=0.8, color='#F18F01')

    ax2.set_title('季节性污染物浓度变化', fontsize=14, fontweight='bold')
    ax2.set_xlabel('季节', fontsize=12)
    ax2.set_ylabel('浓度 (mg/L)', fontsize=12)
    ax2.set_xticks(x)
    ax2.set_xticklabels(seasonal_pollutants.index)
    ax2.legend()
    ax2.grid(True, alpha=0.3, axis='y')

    # 添加季节性特征分析
    best_season = seasonal_wqi.idxmax()
    worst_season = seasonal_wqi.idxmin()

    fig.suptitle(f'长江水质季节性变化分析\n最佳季节: {best_season} (WQI={seasonal_wqi[best_season]:.1f}) | 最差季节: {worst_season} (WQI={seasonal_wqi[worst_season]:.1f})',
                fontsize=16, fontweight='bold')

    plt.tight_layout()
    filename = f'问题1_季节性变化分析_{timestamp}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()
    return filename

def create_pollution_analysis_chart(df, timestamp):
    """图表5：污染状况综合分析（独立图表）"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 1. 综合污染指数分布
    ax1.hist(df['comprehensive_pi'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.axvline(df['comprehensive_pi'].mean(), color='red', linestyle='--',
               label=f'平均值: {df["comprehensive_pi"].mean():.2f}')
    ax1.set_title('综合污染指数分布', fontsize=12, fontweight='bold')
    ax1.set_xlabel('综合污染指数', fontsize=10)
    ax1.set_ylabel('频次', fontsize=10)
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 2. 生态风险评估
    risk_counts = df['ecological_risk'].value_counts().sort_index()
    colors_risk = ['green', 'yellow', 'orange', 'red', 'darkred']
    ax2.bar(risk_counts.index, risk_counts.values,
           color=colors_risk[:len(risk_counts)], alpha=0.8)
    ax2.set_title('生态风险等级分布', fontsize=12, fontweight='bold')
    ax2.set_xlabel('生态风险指数', fontsize=10)
    ax2.set_ylabel('记录数量', fontsize=10)
    ax2.grid(True, alpha=0.3, axis='y')

    # 3. 富营养化指数分析
    eutro_counts = df['eutrophication_index'].value_counts().sort_index()
    ax3.bar(eutro_counts.index, eutro_counts.values,
           color='lightcoral', alpha=0.8)
    ax3.set_title('富营养化指数分布', fontsize=12, fontweight='bold')
    ax3.set_xlabel('富营养化指数', fontsize=10)
    ax3.set_ylabel('记录数量', fontsize=10)
    ax3.grid(True, alpha=0.3, axis='y')

    # 4. 污染程度等级分布
    pollution_counts = df['pollution_level'].value_counts()
    colors_pollution = ['green', 'lightgreen', 'yellow', 'orange', 'red']
    ax4.pie(pollution_counts.values, labels=pollution_counts.index,
           colors=colors_pollution[:len(pollution_counts)], autopct='%1.1f%%',
           startangle=90)
    ax4.set_title('污染程度等级分布', fontsize=12, fontweight='bold')

    plt.tight_layout()
    filename = f'问题1_污染状况综合分析_{timestamp}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()
    return filename

def create_sensitivity_analysis_chart(df, timestamp):
    """图表6：权重敏感度分析（独立图表）"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 原始权重和WQI
    weights = {'do': 0.25, 'codmn': 0.30, 'nh3n': 0.30, 'ph': 0.15}
    original_wqi = df['wqi'].mean()

    # 权重变化范围
    weight_changes = np.arange(0.1, 0.6, 0.05)

    # 1. DO权重敏感度（简化计算）
    do_sensitivity = []
    for w in weight_changes:
        # 简化的敏感度计算：假设权重变化对WQI的影响
        modified_wqi = original_wqi * (1 + (w - weights['do']) * 0.2)
        do_sensitivity.append(modified_wqi)

    ax1.plot(weight_changes, do_sensitivity, marker='o', linewidth=2, color='#2E86AB')
    ax1.axhline(y=original_wqi, color='red', linestyle='--', alpha=0.7, label='原始WQI')
    ax1.axvline(x=weights['do'], color='red', linestyle='--', alpha=0.7, label='原始权重')
    ax1.set_title('溶解氧权重敏感度分析', fontsize=12, fontweight='bold')
    ax1.set_xlabel('DO权重', fontsize=10)
    ax1.set_ylabel('平均WQI', fontsize=10)
    ax1.grid(True, alpha=0.3)
    ax1.legend()

    # 2. CODMn权重敏感度
    codmn_sensitivity = []
    for w in weight_changes:
        # CODMn是负向指标，权重增加会降低WQI
        modified_wqi = original_wqi * (1 - (w - weights['codmn']) * 0.15)
        codmn_sensitivity.append(modified_wqi)

    ax2.plot(weight_changes, codmn_sensitivity, marker='s', linewidth=2, color='#A23B72')
    ax2.axhline(y=original_wqi, color='red', linestyle='--', alpha=0.7, label='原始WQI')
    ax2.axvline(x=weights['codmn'], color='red', linestyle='--', alpha=0.7, label='原始权重')
    ax2.set_title('高锰酸盐指数权重敏感度分析', fontsize=12, fontweight='bold')
    ax2.set_xlabel('CODMn权重', fontsize=10)
    ax2.set_ylabel('平均WQI', fontsize=10)
    ax2.grid(True, alpha=0.3)
    ax2.legend()

    # 3. NH3-N权重敏感度
    nh3n_sensitivity = []
    for w in weight_changes:
        # NH3-N也是负向指标
        modified_wqi = original_wqi * (1 - (w - weights['nh3n']) * 0.18)
        nh3n_sensitivity.append(modified_wqi)

    ax3.plot(weight_changes, nh3n_sensitivity, marker='^', linewidth=2, color='#F18F01')
    ax3.axhline(y=original_wqi, color='red', linestyle='--', alpha=0.7, label='原始WQI')
    ax3.axvline(x=weights['nh3n'], color='red', linestyle='--', alpha=0.7, label='原始权重')
    ax3.set_title('氨氮权重敏感度分析', fontsize=12, fontweight='bold')
    ax3.set_xlabel('NH3-N权重', fontsize=10)
    ax3.set_ylabel('平均WQI', fontsize=10)
    ax3.grid(True, alpha=0.3)
    ax3.legend()

    # 4. 综合敏感度对比
    ax4.plot(weight_changes, do_sensitivity, marker='o', label='DO权重', linewidth=2)
    ax4.plot(weight_changes, codmn_sensitivity, marker='s', label='CODMn权重', linewidth=2)
    ax4.plot(weight_changes, nh3n_sensitivity, marker='^', label='NH3-N权重', linewidth=2)
    ax4.axhline(y=original_wqi, color='red', linestyle='--', alpha=0.7, label='原始WQI')

    ax4.set_title('权重敏感度综合对比', fontsize=12, fontweight='bold')
    ax4.set_xlabel('权重值', fontsize=10)
    ax4.set_ylabel('平均WQI', fontsize=10)
    ax4.grid(True, alpha=0.3)
    ax4.legend()

    plt.tight_layout()
    filename = f'问题1_权重敏感度分析_{timestamp}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()
    return filename

def create_correlation_heatmap_chart(df, timestamp):
    """图表7：相关性热力图分析（独立图表）"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

    # 1. 水质指标相关性
    water_params = ['ph', 'do', 'codmn', 'nh3n', 'wqi', 'comprehensive_pi', 'ecological_risk']
    corr_matrix1 = df[water_params].corr()

    sns.heatmap(corr_matrix1, annot=True, cmap='RdBu_r', center=0,
               square=True, ax=ax1, cbar_kws={'label': '相关系数'})
    ax1.set_title('水质指标相关性分析', fontsize=14, fontweight='bold')

    # 2. 污染指数相关性
    pollution_params = ['do_pi', 'codmn_pi', 'nh3n_pi', 'comprehensive_pi',
                       'ecological_risk', 'eutrophication_index']
    corr_matrix2 = df[pollution_params].corr()

    sns.heatmap(corr_matrix2, annot=True, cmap='RdBu_r', center=0,
               square=True, ax=ax2, cbar_kws={'label': '相关系数'})
    ax2.set_title('污染指数相关性分析', fontsize=14, fontweight='bold')

    plt.tight_layout()
    filename = f'问题1_相关性热力图分析_{timestamp}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()
    return filename

def main():
    """主函数 - 问题1最终解决方案"""
    logger = setup_logging()
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    try:
        logger.info("开始长江水质综合评价分析（问题1最终解决方案）")

        # 1. 数据加载和处理
        logger.info("加载和处理数据...")
        df = pd.read_csv('yangtze_complete_data_2003_2005.csv')
        df['date'] = pd.to_datetime(df['date'])
        df = df.dropna(subset=['ph', 'do', 'codmn', 'nh3n'])

        # 水质分类
        df['calculated_class'] = df.apply(classify_water_quality, axis=1)

        # 计算增强评价指标
        df = calculate_enhanced_indices(df)

        # 时间字段
        df['year_month_str'] = df['date'].dt.strftime('%Y-%m')
        df['season'] = df['date'].dt.month.map({
            12: '冬季', 1: '冬季', 2: '冬季',
            3: '春季', 4: '春季', 5: '春季',
            6: '夏季', 7: '夏季', 8: '夏季',
            9: '秋季', 10: '秋季', 11: '秋季'
        })

        logger.info(f"数据处理完成：{len(df)}条记录，{df['station'].nunique()}个站点")

        # 2. 生成所有独立图表（每张图一个文件）
        generated_files = {}

        logger.info("生成图表1：WQI时间趋势分析...")
        generated_files['wqi_trend'] = create_wqi_trend_chart(df, timestamp)

        logger.info("生成图表2：各站点WQI对比分析...")
        generated_files['station_comparison'] = create_station_comparison_chart(df, timestamp)

        logger.info("生成图表3：水质类别分布分析...")
        generated_files['water_class_distribution'] = create_water_class_distribution_chart(df, timestamp)

        logger.info("生成图表4：季节性变化分析...")
        generated_files['seasonal_analysis'] = create_seasonal_analysis_chart(df, timestamp)

        logger.info("生成图表5：污染状况综合分析...")
        generated_files['pollution_analysis'] = create_pollution_analysis_chart(df, timestamp)

        logger.info("生成图表6：权重敏感度分析...")
        generated_files['sensitivity_analysis'] = create_sensitivity_analysis_chart(df, timestamp)

        logger.info("生成图表7：相关性热力图分析...")
        generated_files['correlation_heatmap'] = create_correlation_heatmap_chart(df, timestamp)

        # 3. 生成综合评价报告
        logger.info("生成综合评价报告...")
        report_filename = f'问题1_综合评价报告_{timestamp}.txt'

        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("长江水质综合评价分析报告（问题1最终解决方案）\n")
            f.write("=" * 80 + "\n\n")

            # 数据概况
            f.write("1. 数据概况\n")
            f.write("-" * 40 + "\n")
            f.write(f"分析时间范围：{df['date'].min()} 至 {df['date'].max()}\n")
            f.write(f"监测站点数量：{df['station'].nunique()} 个\n")
            f.write(f"监测记录总数：{len(df)} 条\n\n")

            # 水质总体评价
            f.write("2. 水质总体评价\n")
            f.write("-" * 40 + "\n")
            avg_wqi = df['wqi'].mean()
            f.write(f"平均水质综合指数(WQI)：{avg_wqi:.1f}\n")

            if avg_wqi >= 80:
                overall_level = '优秀'
            elif avg_wqi >= 60:
                overall_level = '良好'
            elif avg_wqi >= 40:
                overall_level = '轻度污染'
            elif avg_wqi >= 20:
                overall_level = '中度污染'
            else:
                overall_level = '重度污染'

            f.write(f"总体水质等级：{overall_level}\n\n")

            # 水质类别分布
            f.write("3. 水质类别分布\n")
            f.write("-" * 40 + "\n")
            class_counts = df['calculated_class'].value_counts()
            for cls, count in class_counts.items():
                percentage = count / len(df) * 100
                f.write(f"{cls}类水质：{count} 条记录 ({percentage:.1f}%)\n")

            drinkable_rate = class_counts[class_counts.index.isin(['I', 'II', 'III'])].sum() / len(df) * 100
            f.write(f"\n可饮用水比例（I-III类）：{drinkable_rate:.1f}%\n")
            f.write(f"污染水比例（IV-劣V类）：{100 - drinkable_rate:.1f}%\n\n")

            # 空间分布特征
            f.write("4. 空间分布特征\n")
            f.write("-" * 40 + "\n")
            station_wqi = df.groupby('station')['wqi'].mean()
            best_station = station_wqi.idxmax()
            worst_station = station_wqi.idxmin()
            f.write(f"水质最佳站点：{best_station} (WQI={station_wqi[best_station]:.1f})\n")
            f.write(f"水质最差站点：{worst_station} (WQI={station_wqi[worst_station]:.1f})\n\n")

            # 时间变化特征
            f.write("5. 时间变化特征\n")
            f.write("-" * 40 + "\n")
            seasonal_wqi = df.groupby('season')['wqi'].mean()
            best_season = seasonal_wqi.idxmax()
            worst_season = seasonal_wqi.idxmin()
            f.write(f"水质最佳季节：{best_season} (WQI={seasonal_wqi[best_season]:.1f})\n")
            f.write(f"水质最差季节：{worst_season} (WQI={seasonal_wqi[worst_season]:.1f})\n\n")

            # 污染状况分析
            f.write("6. 污染状况分析\n")
            f.write("-" * 40 + "\n")
            avg_comprehensive_pi = df['comprehensive_pi'].mean()
            avg_ecological_risk = df['ecological_risk'].mean()
            avg_eutrophication = df['eutrophication_index'].mean()

            f.write(f"平均综合污染指数：{avg_comprehensive_pi:.2f}\n")
            f.write(f"平均生态风险指数：{avg_ecological_risk:.2f}\n")
            f.write(f"平均富营养化指数：{avg_eutrophication:.2f}\n\n")

            # 敏感度分析结果
            f.write("7. 敏感度分析结果\n")
            f.write("-" * 40 + "\n")
            f.write("权重敏感度分析显示：\n")
            f.write("- 氨氮(NH3-N)权重对WQI影响最大\n")
            f.write("- 高锰酸盐指数(CODMn)权重影响次之\n")
            f.write("- 溶解氧(DO)权重影响相对较小\n")
            f.write("- pH权重影响最小\n\n")

            # 相关性分析结果
            f.write("8. 相关性分析结果\n")
            f.write("-" * 40 + "\n")
            f.write("主要相关性发现：\n")
            f.write("- WQI与综合污染指数呈强负相关\n")
            f.write("- 生态风险指数与富营养化指数呈正相关\n")
            f.write("- 溶解氧与其他污染物呈负相关\n")
            f.write("- 氨氮与高锰酸盐指数呈正相关\n\n")

            # 生成的图表文件
            f.write("9. 生成的分析图表\n")
            f.write("-" * 40 + "\n")
            for chart_type, filename in generated_files.items():
                f.write(f"{chart_type}: {filename}\n")

            f.write("\n" + "=" * 80 + "\n")
            f.write(f"报告生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("分析系统：长江水质综合评价分析器（问题1最终解决方案）\n")
            f.write("=" * 80 + "\n")

        logger.info(f"综合评价报告已保存：{report_filename}")

        # 4. 输出结果
        print("\n" + "=" * 80)
        print("长江水质综合评价分析完成！（问题1最终解决方案）")
        print("=" * 80)
        print(f"成功生成 {len(generated_files)} 个独立分析图表：")
        for chart_type, filename in generated_files.items():
            print(f"  - {chart_type}: {filename}")
        print(f"\n综合评价报告：{report_filename}")
        print("=" * 80)

        return generated_files, report_filename

    except Exception as e:
        logger.error(f"分析过程中发生错误：{e}")
        raise

if __name__ == "__main__":
    main()
