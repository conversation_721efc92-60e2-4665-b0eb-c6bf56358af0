#!/usr/bin/env python3
"""
长江干流主要观测站点水流数据转换器

功能：
1. 解析长江干流7个主要观测站点的水流数据
2. 转换为标准CSV格式（长格式和宽格式）
3. 提供数据验证和摘要分析
4. 确保与水质数据的兼容性

作者：长江水质分析系统
日期：2025-07-28
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import os


class YangtzeFlowDataConverter:
    """长江水流数据转换器"""
    
    def __init__(self):
        """初始化转换器"""
        self.setup_logging()
        
        # 站点基本信息
        self.stations_info = {
            '四川攀枝花': {
                'code': 'SC_PZH',
                'distance_km': 0,
                'full_name': '四川攀枝花龙洞',
                'province': '四川'
            },
            '重庆朱沱': {
                'code': 'CQ_ZT', 
                'distance_km': 950,
                'full_name': '重庆朱沱',
                'province': '重庆'
            },
            '湖北宜昌': {
                'code': 'HB_YC',
                'distance_km': 1728,
                'full_name': '湖北宜昌南津关',
                'province': '湖北'
            },
            '湖南岳阳': {
                'code': 'HN_YY',
                'distance_km': 2123,
                'full_name': '湖南岳阳城陵矶',
                'province': '湖南'
            },
            '江西九江': {
                'code': 'JX_JJ',
                'distance_km': 2623,
                'full_name': '江西九江河西水厂',
                'province': '江西'
            },
            '安徽安庆': {
                'code': 'AH_AQ',
                'distance_km': 2787,
                'full_name': '安徽安庆皖河口',
                'province': '安徽'
            },
            '江苏南京': {
                'code': 'JS_NJ',
                'distance_km': 3251,
                'full_name': '江苏南京林山',
                'province': '江苏'
            }
        }
        
        # 原始水流数据
        self.flow_data = {
            '2004-04': {
                '四川攀枝花': {'flow_rate': 3690, 'flow_velocity': 3.7},
                '重庆朱沱': {'flow_rate': 13800, 'flow_velocity': 2.1},
                '湖北宜昌': {'flow_rate': 21000, 'flow_velocity': 0.9},
                '湖南岳阳': {'flow_rate': 25600, 'flow_velocity': 0.9},
                '江西九江': {'flow_rate': 28100, 'flow_velocity': 1.0},
                '安徽安庆': {'flow_rate': 29500, 'flow_velocity': 1.1},
                '江苏南京': {'flow_rate': 29800, 'flow_velocity': 1.2}
            },
            '2004-05': {
                '四川攀枝花': {'flow_rate': 3720, 'flow_velocity': 3.7},
                '重庆朱沱': {'flow_rate': 13100, 'flow_velocity': 1.9},
                '湖北宜昌': {'flow_rate': 19800, 'flow_velocity': 0.8},
                '湖南岳阳': {'flow_rate': 20500, 'flow_velocity': 0.9},
                '江西九江': {'flow_rate': 29800, 'flow_velocity': 1.1},
                '安徽安庆': {'flow_rate': 34000, 'flow_velocity': 1.1},
                '江苏南京': {'flow_rate': 34500, 'flow_velocity': 1.2}
            },
            '2004-06': {
                '四川攀枝花': {'flow_rate': 4010, 'flow_velocity': 3.9},
                '重庆朱沱': {'flow_rate': 14200, 'flow_velocity': 2.1},
                '湖北宜昌': {'flow_rate': 20300, 'flow_velocity': 1.2},
                '湖南岳阳': {'flow_rate': 22600, 'flow_velocity': 1.3},
                '江西九江': {'flow_rate': 29500, 'flow_velocity': 1.5},
                '安徽安庆': {'flow_rate': 32100, 'flow_velocity': 1.5},
                '江苏南京': {'flow_rate': 33100, 'flow_velocity': 1.6}
            },
            '2004-07': {
                '四川攀枝花': {'flow_rate': 4660, 'flow_velocity': 4.1},
                '重庆朱沱': {'flow_rate': 16400, 'flow_velocity': 2.3},
                '湖北宜昌': {'flow_rate': 22700, 'flow_velocity': 1.4},
                '湖南岳阳': {'flow_rate': 24100, 'flow_velocity': 1.5},
                '江西九江': {'flow_rate': 27000, 'flow_velocity': 1.5},
                '安徽安庆': {'flow_rate': 31900, 'flow_velocity': 1.6},
                '江苏南京': {'flow_rate': 32100, 'flow_velocity': 1.7}
            },
            '2004-08': {
                '四川攀枝花': {'flow_rate': 3740, 'flow_velocity': 3.8},
                '重庆朱沱': {'flow_rate': 10600, 'flow_velocity': 2.1},
                '湖北宜昌': {'flow_rate': 24000, 'flow_velocity': 1.4},
                '湖南岳阳': {'flow_rate': 25900, 'flow_velocity': 1.4},
                '江西九江': {'flow_rate': 32100, 'flow_velocity': 1.5},
                '安徽安庆': {'flow_rate': 33400, 'flow_velocity': 1.7},
                '江苏南京': {'flow_rate': 35100, 'flow_velocity': 1.7}
            },
            '2004-09': {
                '四川攀枝花': {'flow_rate': 6280, 'flow_velocity': 5.1},
                '重庆朱沱': {'flow_rate': 47600, 'flow_velocity': 4.8},
                '湖北宜昌': {'flow_rate': 53500, 'flow_velocity': 1.7},
                '湖南岳阳': {'flow_rate': 53800, 'flow_velocity': 1.9},
                '江西九江': {'flow_rate': 72800, 'flow_velocity': 2.1},
                '安徽安庆': {'flow_rate': 74200, 'flow_velocity': 3.4},
                '江苏南京': {'flow_rate': 81000, 'flow_velocity': 3.4}
            },
            '2004-10': {
                '四川攀枝花': {'flow_rate': 3260, 'flow_velocity': 3.1},
                '重庆朱沱': {'flow_rate': 16200, 'flow_velocity': 2.3},
                '湖北宜昌': {'flow_rate': 19100, 'flow_velocity': 1.5},
                '湖南岳阳': {'flow_rate': 22300, 'flow_velocity': 1.6},
                '江西九江': {'flow_rate': 24800, 'flow_velocity': 1.6},
                '安徽安庆': {'flow_rate': 31000, 'flow_velocity': 1.7},
                '江苏南京': {'flow_rate': 38400, 'flow_velocity': 1.9}
            },
            '2004-11': {
                '四川攀枝花': {'flow_rate': 1500, 'flow_velocity': 2.7},
                '重庆朱沱': {'flow_rate': 8170, 'flow_velocity': 1.9},
                '湖北宜昌': {'flow_rate': 10600, 'flow_velocity': 0.7},
                '湖南岳阳': {'flow_rate': 12000, 'flow_velocity': 0.8},
                '江西九江': {'flow_rate': 14600, 'flow_velocity': 0.9},
                '安徽安庆': {'flow_rate': 17000, 'flow_velocity': 0.9},
                '江苏南京': {'flow_rate': 19600, 'flow_velocity': 1.0}
            },
            '2004-12': {
                '四川攀枝花': {'flow_rate': 951, 'flow_velocity': 3.1},
                '重庆朱沱': {'flow_rate': 6550, 'flow_velocity': 1.5},
                '湖北宜昌': {'flow_rate': 7400, 'flow_velocity': 0.7},
                '湖南岳阳': {'flow_rate': 10700, 'flow_velocity': 0.8},
                '江西九江': {'flow_rate': 13200, 'flow_velocity': 0.8},
                '安徽安庆': {'flow_rate': 14100, 'flow_velocity': 0.8},
                '江苏南京': {'flow_rate': 14900, 'flow_velocity': 0.9}
            },
            '2005-01': {
                '四川攀枝花': {'flow_rate': 712, 'flow_velocity': 2.1},
                '重庆朱沱': {'flow_rate': 4020, 'flow_velocity': 1.5},
                '湖北宜昌': {'flow_rate': 4570, 'flow_velocity': 0.5},
                '湖南岳阳': {'flow_rate': 8190, 'flow_velocity': 0.6},
                '江西九江': {'flow_rate': 10900, 'flow_velocity': 0.7},
                '安徽安庆': {'flow_rate': 12300, 'flow_velocity': 0.7},
                '江苏南京': {'flow_rate': 14400, 'flow_velocity': 0.8}
            },
            '2005-02': {
                '四川攀枝花': {'flow_rate': 612, 'flow_velocity': 2.0},
                '重庆朱沱': {'flow_rate': 3603, 'flow_velocity': 1.0},
                '湖北宜昌': {'flow_rate': 4510, 'flow_velocity': 0.4},
                '湖南岳阳': {'flow_rate': 7980, 'flow_velocity': 0.6},
                '江西九江': {'flow_rate': 10300, 'flow_velocity': 0.7},
                '安徽安庆': {'flow_rate': 13700, 'flow_velocity': 0.7},
                '江苏南京': {'flow_rate': 15100, 'flow_velocity': 0.8}
            },
            '2005-03': {
                '四川攀枝花': {'flow_rate': 623, 'flow_velocity': 1.9},
                '重庆朱沱': {'flow_rate': 4740, 'flow_velocity': 0.9},
                '湖北宜昌': {'flow_rate': 5180, 'flow_velocity': 0.4},
                '湖南岳阳': {'flow_rate': 7040, 'flow_velocity': 0.6},
                '江西九江': {'flow_rate': 14300, 'flow_velocity': 0.8},
                '安徽安庆': {'flow_rate': 21400, 'flow_velocity': 0.8},
                '江苏南京': {'flow_rate': 21500, 'flow_velocity': 0.9}
            },
            '2005-04': {
                '四川攀枝花': {'flow_rate': 642, 'flow_velocity': 2.1},
                '重庆朱沱': {'flow_rate': 3650, 'flow_velocity': 1.2},
                '湖北宜昌': {'flow_rate': 5400, 'flow_velocity': 0.4},
                '湖南岳阳': {'flow_rate': 7240, 'flow_velocity': 0.5},
                '江西九江': {'flow_rate': 15100, 'flow_velocity': 0.7},
                '安徽安庆': {'flow_rate': 20200, 'flow_velocity': 0.8},
                '江苏南京': {'flow_rate': 22100, 'flow_velocity': 0.8}
            }
        }
        
        self.logger.info("长江水流数据转换器初始化完成")

    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger('yangtze_flow_converter')

    def convert_to_long_format(self) -> pd.DataFrame:
        """
        转换为长格式DataFrame

        Returns:
            pd.DataFrame: 长格式的水流数据
        """
        records = []

        for date_str, stations_data in self.flow_data.items():
            for station_name, flow_data in stations_data.items():
                station_info = self.stations_info[station_name]

                record = {
                    'date': date_str,
                    'year': int(date_str.split('-')[0]),
                    'month': int(date_str.split('-')[1]),
                    'station': station_name,
                    'station_code': station_info['code'],
                    'station_full_name': station_info['full_name'],
                    'province': station_info['province'],
                    'distance_km': station_info['distance_km'],
                    'flow_rate_m3s': flow_data['flow_rate'],
                    'flow_velocity_ms': flow_data['flow_velocity']
                }
                records.append(record)

        df = pd.DataFrame(records)

        # 按日期和距离排序
        df = df.sort_values(['date', 'distance_km']).reset_index(drop=True)

        self.logger.info(f"转换为长格式完成，共{len(df)}条记录")
        return df

    def convert_to_wide_format(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        转换为宽格式DataFrame

        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: (水流量宽格式, 水流速宽格式)
        """
        long_df = self.convert_to_long_format()

        # 水流量宽格式
        flow_rate_wide = long_df.pivot(
            index='date',
            columns='station',
            values='flow_rate_m3s'
        ).reset_index()

        # 水流速宽格式
        flow_velocity_wide = long_df.pivot(
            index='date',
            columns='station',
            values='flow_velocity_ms'
        ).reset_index()

        self.logger.info("转换为宽格式完成")
        return flow_rate_wide, flow_velocity_wide

    def validate_data(self, df: pd.DataFrame) -> Dict:
        """
        验证数据质量

        Args:
            df: 要验证的DataFrame

        Returns:
            Dict: 验证结果
        """
        validation_results = {
            'total_records': len(df),
            'date_range': f"{df['date'].min()} 至 {df['date'].max()}",
            'stations_count': df['station'].nunique(),
            'missing_values': df.isnull().sum().to_dict(),
            'data_quality_issues': []
        }

        # 检查数值范围
        if (df['flow_rate_m3s'] <= 0).any():
            validation_results['data_quality_issues'].append("发现水流量<=0的异常值")

        if (df['flow_velocity_ms'] <= 0).any():
            validation_results['data_quality_issues'].append("发现水流速<=0的异常值")

        # 检查距离单调性
        stations_by_distance = df.groupby('station')['distance_km'].first().sort_values()
        if not stations_by_distance.is_monotonic_increasing:
            validation_results['data_quality_issues'].append("站点距离不是单调递增的")

        # 检查时间连续性
        unique_dates = sorted(df['date'].unique())
        if len(unique_dates) != 13:
            validation_results['data_quality_issues'].append(f"时间序列不完整，期望13个月，实际{len(unique_dates)}个月")

        self.logger.info(f"数据验证完成，发现{len(validation_results['data_quality_issues'])}个问题")
        return validation_results

    def export_to_csv(self) -> Dict[str, str]:
        """
        导出CSV文件

        Returns:
            Dict[str, str]: 导出的文件路径
        """
        exported_files = {}

        # 导出长格式CSV
        long_df = self.convert_to_long_format()
        long_file = 'yangtze_flow_data_long.csv'
        long_df.to_csv(long_file, index=False, encoding='utf-8-sig')
        exported_files['long_format'] = long_file
        self.logger.info(f"长格式数据已导出到: {long_file}")

        # 导出宽格式CSV
        flow_rate_wide, flow_velocity_wide = self.convert_to_wide_format()

        # 水流量宽格式
        flow_rate_file = 'yangtze_flow_rate_wide.csv'
        flow_rate_wide.to_csv(flow_rate_file, index=False, encoding='utf-8-sig')
        exported_files['flow_rate_wide'] = flow_rate_file

        # 水流速宽格式
        flow_velocity_file = 'yangtze_flow_velocity_wide.csv'
        flow_velocity_wide.to_csv(flow_velocity_file, index=False, encoding='utf-8-sig')
        exported_files['flow_velocity_wide'] = flow_velocity_file

        self.logger.info(f"宽格式数据已导出到: {flow_rate_file}, {flow_velocity_file}")

        return exported_files

    def generate_summary_report(self) -> str:
        """
        生成数据摘要报告

        Returns:
            str: 报告文件路径
        """
        long_df = self.convert_to_long_format()
        validation_results = self.validate_data(long_df)

        # 统计分析
        stats = {
            'flow_rate_stats': long_df['flow_rate_m3s'].describe(),
            'flow_velocity_stats': long_df['flow_velocity_ms'].describe(),
            'monthly_avg_flow': long_df.groupby('date')['flow_rate_m3s'].mean(),
            'station_avg_flow': long_df.groupby('station')['flow_rate_m3s'].mean(),
            'seasonal_analysis': self._analyze_seasonal_patterns(long_df)
        }

        # 生成报告
        report_content = f"""
长江干流主要观测站点水流数据分析报告
==========================================

数据概况：
- 总记录数：{validation_results['total_records']}条
- 时间范围：{validation_results['date_range']}
- 监测站点：{validation_results['stations_count']}个
- 数据来源：《长江年鉴》相关资料

站点信息：
"""

        for station, info in self.stations_info.items():
            report_content += f"- {station}（{info['code']}）：距离{info['distance_km']}km，对应{info['full_name']}\n"

        report_content += f"""
水流量统计（m³/s）：
- 平均值：{stats['flow_rate_stats']['mean']:.1f}
- 中位数：{stats['flow_rate_stats']['50%']:.1f}
- 最小值：{stats['flow_rate_stats']['min']:.1f}
- 最大值：{stats['flow_rate_stats']['max']:.1f}
- 标准差：{stats['flow_rate_stats']['std']:.1f}

水流速统计（m/s）：
- 平均值：{stats['flow_velocity_stats']['mean']:.2f}
- 中位数：{stats['flow_velocity_stats']['50%']:.2f}
- 最小值：{stats['flow_velocity_stats']['min']:.2f}
- 最大值：{stats['flow_velocity_stats']['max']:.2f}
- 标准差：{stats['flow_velocity_stats']['std']:.2f}

月度平均水流量（m³/s）：
"""

        for date, flow in stats['monthly_avg_flow'].items():
            report_content += f"- {date}：{flow:.1f}\n"

        report_content += "\n站点平均水流量（m³/s）：\n"
        for station, flow in stats['station_avg_flow'].items():
            report_content += f"- {station}：{flow:.1f}\n"

        report_content += f"""
季节性分析：
{stats['seasonal_analysis']}

数据质量检查：
- 缺失值：{sum(validation_results['missing_values'].values())}个
- 质量问题：{len(validation_results['data_quality_issues'])}个
"""

        if validation_results['data_quality_issues']:
            report_content += "问题详情：\n"
            for issue in validation_results['data_quality_issues']:
                report_content += f"  - {issue}\n"

        report_content += f"""
文件输出：
- 长格式CSV：yangtze_flow_data_long.csv（推荐用于数据分析）
- 水流量宽格式CSV：yangtze_flow_rate_wide.csv
- 水流速宽格式CSV：yangtze_flow_velocity_wide.csv

数据说明：
1. 水流量和水流速均为年平均值
2. 距离为从四川攀枝花开始的累计距离
3. 站点名称已与水质数据保持一致，便于关联分析
4. 数据时间范围：2004年4月-2005年4月

生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        # 保存报告
        report_file = 'yangtze_flow_data_summary.txt'
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        self.logger.info(f"数据摘要报告已生成：{report_file}")
        return report_file

    def _analyze_seasonal_patterns(self, df: pd.DataFrame) -> str:
        """分析季节性模式"""
        # 按季节分组
        df['season'] = df['month'].map({
            1: '冬季', 2: '冬季', 3: '春季', 4: '春季', 5: '春季', 6: '夏季',
            7: '夏季', 8: '夏季', 9: '秋季', 10: '秋季', 11: '秋季', 12: '冬季'
        })

        seasonal_stats = df.groupby('season').agg({
            'flow_rate_m3s': ['mean', 'max', 'min'],
            'flow_velocity_ms': ['mean', 'max', 'min']
        }).round(2)

        analysis = "季节性水流特征：\n"
        for season in ['春季', '夏季', '秋季', '冬季']:
            if season in seasonal_stats.index:
                flow_mean = seasonal_stats.loc[season, ('flow_rate_m3s', 'mean')]
                velocity_mean = seasonal_stats.loc[season, ('flow_velocity_ms', 'mean')]
                analysis += f"- {season}：平均水流量{flow_mean}m³/s，平均水流速{velocity_mean}m/s\n"

        return analysis

    def process_all(self) -> Dict:
        """
        执行完整的数据处理流程

        Returns:
            Dict: 处理结果摘要
        """
        self.logger.info("开始执行完整数据处理流程...")

        # 导出CSV文件
        exported_files = self.export_to_csv()

        # 生成摘要报告
        report_file = self.generate_summary_report()

        # 数据验证
        long_df = self.convert_to_long_format()
        validation_results = self.validate_data(long_df)

        results = {
            'exported_files': exported_files,
            'report_file': report_file,
            'validation_results': validation_results,
            'total_records': len(long_df),
            'processing_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        self.logger.info("数据处理流程完成")
        return results
