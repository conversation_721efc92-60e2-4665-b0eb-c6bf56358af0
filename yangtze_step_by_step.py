#!/usr/bin/env python3
"""
逐步测试版本 - 一步一步验证功能
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
from mpl_toolkits.mplot3d import Axes3D
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import logging
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def setup_logging():
    logging.basicConfig(level=logging.INFO, 
                      format='%(asctime)s - %(levelname)s - %(message)s')
    return logging.getLogger('step_test')

def classify_water_quality(row):
    """水质分类"""
    ph, do, codmn, nh3n = row['ph'], row['do'], row['codmn'], row['nh3n']
    
    if ph < 6.0 or ph > 9.0:
        return '劣V'
    if do < 2.0 or codmn > 15.0 or nh3n > 2.0:
        return '劣V'
    elif do < 3.0 or codmn > 10.0 or nh3n > 1.5:
        return 'V'
    elif do < 5.0 or codmn > 6.0 or nh3n > 1.0:
        return 'IV'
    elif do < 6.0 or codmn > 4.0 or nh3n > 0.5:
        return 'III'
    elif do < 7.5 or codmn > 2.0 or nh3n > 0.15:
        return 'II'
    else:
        return 'I'

def test_step1_basic_charts():
    """测试步骤1：基础图表"""
    logger = setup_logging()
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    try:
        logger.info("步骤1：测试基础图表生成...")
        
        # 加载数据
        df = pd.read_csv('yangtze_complete_data_2003_2005.csv')
        df['date'] = pd.to_datetime(df['date'])
        df = df.dropna(subset=['ph', 'do', 'codmn', 'nh3n'])
        
        # 水质分类
        df['calculated_class'] = df.apply(classify_water_quality, axis=1)
        class_scores = {'I': 100, 'II': 80, 'III': 60, 'IV': 40, 'V': 20, '劣V': 0}
        df['wqi'] = df['calculated_class'].map(class_scores)
        
        # 时间字段
        df['year_month_str'] = df['date'].dt.strftime('%Y-%m')
        
        logger.info(f"数据处理完成：{len(df)}条记录")
        
        # 1. WQI趋势图
        logger.info("生成WQI趋势图...")
        fig, ax = plt.subplots(figsize=(12, 6))
        
        monthly_wqi = df.groupby('year_month_str')['wqi'].mean()
        ax.plot(range(len(monthly_wqi)), monthly_wqi.values, marker='o', linewidth=2)
        ax.set_title('长江水质WQI时间趋势', fontsize=14, fontweight='bold')
        ax.set_xlabel('时间序列', fontsize=12)
        ax.set_ylabel('WQI值', fontsize=12)
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        filename1 = f'问题1_步骤1_WQI趋势_{timestamp}.png'
        plt.savefig(filename1, dpi=300, bbox_inches='tight')
        plt.close()
        logger.info(f"WQI趋势图已保存：{filename1}")
        
        # 2. 站点对比图
        logger.info("生成站点对比图...")
        fig, ax = plt.subplots(figsize=(12, 8))
        
        station_wqi = df.groupby('station')['wqi'].mean().sort_values()
        colors = ['red' if x < 40 else 'orange' if x < 60 else 'yellow' if x < 80 else 'green'
                 for x in station_wqi.values]
        
        bars = ax.barh(range(len(station_wqi)), station_wqi.values, color=colors, alpha=0.7)
        ax.set_yticks(range(len(station_wqi)))
        ax.set_yticklabels([s[:10] + '...' if len(s) > 10 else s for s in station_wqi.index], fontsize=9)
        
        ax.set_title('各监测站点WQI对比', fontsize=14, fontweight='bold')
        ax.set_xlabel('WQI值', fontsize=12)
        ax.grid(True, alpha=0.3, axis='x')
        
        plt.tight_layout()
        filename2 = f'问题1_步骤1_站点对比_{timestamp}.png'
        plt.savefig(filename2, dpi=300, bbox_inches='tight')
        plt.close()
        logger.info(f"站点对比图已保存：{filename2}")
        
        print(f"步骤1完成！生成文件：{filename1}, {filename2}")
        return True
        
    except Exception as e:
        logger.error(f"步骤1失败：{e}")
        import traceback
        traceback.print_exc()
        return False

def test_step2_3d_charts():
    """测试步骤2：三维图表"""
    logger = setup_logging()
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    try:
        logger.info("步骤2：测试三维图表生成...")
        
        # 加载数据
        df = pd.read_csv('yangtze_complete_data_2003_2005.csv')
        df['date'] = pd.to_datetime(df['date'])
        df = df.dropna(subset=['ph', 'do', 'codmn', 'nh3n'])
        
        # 水质分类
        df['calculated_class'] = df.apply(classify_water_quality, axis=1)
        class_scores = {'I': 100, 'II': 80, 'III': 60, 'IV': 40, 'V': 20, '劣V': 0}
        df['wqi'] = df['calculated_class'].map(class_scores)
        
        # 站点编码
        stations = df['station'].unique()
        station_codes = {station: i for i, station in enumerate(stations)}
        df['station_code'] = df['station'].map(station_codes)
        
        logger.info(f"数据处理完成：{len(df)}条记录")
        
        # 3D散点图
        logger.info("生成3D散点图...")
        fig = plt.figure(figsize=(12, 8))
        ax = fig.add_subplot(111, projection='3d')
        
        x = df['station_code']
        y = df['date'].dt.dayofyear
        z = df['wqi']
        colors = df['wqi']
        
        scatter = ax.scatter(x, y, z, c=colors, cmap='RdYlGn', s=30, alpha=0.6)
        
        ax.set_xlabel('监测站点编码', fontsize=10)
        ax.set_ylabel('年内天数', fontsize=10)
        ax.set_zlabel('WQI值', fontsize=10)
        ax.set_title('时空WQI分布三维散点图', fontsize=12, fontweight='bold')
        
        plt.colorbar(scatter, shrink=0.5, aspect=20, label='WQI值')
        
        filename3 = f'问题1_步骤2_3D散点图_{timestamp}.png'
        plt.savefig(filename3, dpi=300, bbox_inches='tight')
        plt.close()
        logger.info(f"3D散点图已保存：{filename3}")
        
        print(f"步骤2完成！生成文件：{filename3}")
        return True
        
    except Exception as e:
        logger.error(f"步骤2失败：{e}")
        import traceback
        traceback.print_exc()
        return False

def test_step3_sensitivity():
    """测试步骤3：敏感度分析"""
    logger = setup_logging()
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    try:
        logger.info("步骤3：测试敏感度分析...")
        
        # 加载数据
        df = pd.read_csv('yangtze_complete_data_2003_2005.csv')
        df['date'] = pd.to_datetime(df['date'])
        df = df.dropna(subset=['ph', 'do', 'codmn', 'nh3n'])
        
        # 水质分类
        df['calculated_class'] = df.apply(classify_water_quality, axis=1)
        class_scores = {'I': 100, 'II': 80, 'III': 60, 'IV': 40, 'V': 20, '劣V': 0}
        df['wqi'] = df['calculated_class'].map(class_scores)
        
        logger.info(f"数据处理完成：{len(df)}条记录")
        
        # 敏感度分析
        logger.info("生成敏感度分析图...")
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # 简化的敏感度分析
        original_wqi = df['wqi'].mean()
        weight_changes = np.arange(0.1, 0.6, 0.05)
        
        # DO权重敏感度（简化版）
        do_sensitivity = []
        for w in weight_changes:
            # 简化计算：假设权重变化对WQI的影响
            modified_wqi = original_wqi * (1 + (w - 0.25) * 0.1)  # 简化公式
            do_sensitivity.append(modified_wqi)
        
        ax.plot(weight_changes, do_sensitivity, marker='o', linewidth=2, label='DO权重敏感度')
        ax.axhline(y=original_wqi, color='red', linestyle='--', alpha=0.7, label='原始WQI')
        ax.axvline(x=0.25, color='red', linestyle='--', alpha=0.7, label='原始权重')
        
        ax.set_title('溶解氧权重敏感度分析', fontsize=14, fontweight='bold')
        ax.set_xlabel('DO权重', fontsize=12)
        ax.set_ylabel('平均WQI', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        plt.tight_layout()
        filename4 = f'问题1_步骤3_敏感度分析_{timestamp}.png'
        plt.savefig(filename4, dpi=300, bbox_inches='tight')
        plt.close()
        logger.info(f"敏感度分析图已保存：{filename4}")
        
        print(f"步骤3完成！生成文件：{filename4}")
        return True
        
    except Exception as e:
        logger.error(f"步骤3失败：{e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数 - 逐步测试"""
    print("开始逐步测试...")
    
    # 步骤1：基础图表
    if test_step1_basic_charts():
        print("✓ 步骤1：基础图表测试成功")
    else:
        print("✗ 步骤1：基础图表测试失败")
        return
    
    # 步骤2：三维图表
    if test_step2_3d_charts():
        print("✓ 步骤2：三维图表测试成功")
    else:
        print("✗ 步骤2：三维图表测试失败")
        return
    
    # 步骤3：敏感度分析
    if test_step3_sensitivity():
        print("✓ 步骤3：敏感度分析测试成功")
    else:
        print("✗ 步骤3：敏感度分析测试失败")
        return
    
    print("\n" + "="*50)
    print("所有步骤测试完成！")
    print("="*50)

if __name__ == "__main__":
    main()
