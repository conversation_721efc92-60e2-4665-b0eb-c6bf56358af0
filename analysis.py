"""
水质分析和可视化模块
提供数据分析和图表生成功能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Optional
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class WaterQualityAnalyzer:
    """水质分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.colors = {
            1: '#2E8B57',  # I类 - 深绿色
            2: '#32CD32',  # II类 - 绿色
            3: '#FFD700',  # III类 - 金色
            4: '#FF8C00',  # IV类 - 橙色
            5: '#FF4500',  # V类 - 红橙色
            6: '#DC143C'   # 劣V类 - 深红色
        }
        
    def plot_station_quality_distribution(self, df: pd.DataFrame, save_path: Optional[str] = None):
        """
        绘制各站点水质类别分布图
        
        Args:
            df: 包含评价结果的数据框
            save_path: 保存路径
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        
        # 各站点水质类别分布
        station_class_counts = df.groupby(['station', 'water_class']).size().unstack(fill_value=0)
        station_class_pct = station_class_counts.div(station_class_counts.sum(axis=1), axis=0) * 100
        
        # 堆叠柱状图
        station_class_pct.plot(kind='bar', stacked=True, ax=ax1, 
                              color=[self.colors.get(i, '#808080') for i in station_class_pct.columns])
        ax1.set_title('各站点水质类别分布（百分比）', fontsize=14, fontweight='bold')
        ax1.set_xlabel('监测站点', fontsize=12)
        ax1.set_ylabel('百分比 (%)', fontsize=12)
        ax1.legend(title='水质类别', labels=[f'{i}类' if i <= 5 else '劣V类' for i in station_class_pct.columns])
        ax1.tick_params(axis='x', rotation=45)
        
        # 各站点达标率
        qualification_rates = df.groupby('station')['is_qualified'].mean() * 100
        bars = ax2.bar(qualification_rates.index, qualification_rates.values, 
                      color=['#2E8B57' if rate >= 80 else '#FF8C00' if rate >= 60 else '#DC143C' 
                             for rate in qualification_rates.values])
        ax2.set_title('各站点水质达标率', fontsize=14, fontweight='bold')
        ax2.set_xlabel('监测站点', fontsize=12)
        ax2.set_ylabel('达标率 (%)', fontsize=12)
        ax2.tick_params(axis='x', rotation=45)
        ax2.axhline(y=80, color='red', linestyle='--', alpha=0.7, label='目标达标率(80%)')
        ax2.legend()
        
        # 添加数值标签
        for bar, rate in zip(bars, qualification_rates.values):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                    f'{rate:.1f}%', ha='center', va='bottom', fontsize=10)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

    def plot_temporal_trends(self, df: pd.DataFrame, save_path: Optional[str] = None):
        """
        绘制时间趋势图
        
        Args:
            df: 包含时间数据的数据框
            save_path: 保存路径
        """
        if 'date' not in df.columns:
            print("数据中没有时间信息，无法绘制时间趋势图")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # 按月聚合数据
        monthly_data = df.groupby([df['date'].dt.to_period('M')]).agg({
            'pollution_index': 'mean',
            'is_qualified': 'mean',
            'ph': 'mean',
            'do': 'mean',
            'codmn': 'mean',
            'nh3n': 'mean'
        })
        
        # 污染指数趋势
        axes[0, 0].plot(monthly_data.index.astype(str), monthly_data['pollution_index'], 
                       marker='o', linewidth=2, markersize=4)
        axes[0, 0].set_title('污染指数时间趋势', fontsize=14, fontweight='bold')
        axes[0, 0].set_ylabel('污染指数', fontsize=12)
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 达标率趋势
        axes[0, 1].plot(monthly_data.index.astype(str), monthly_data['is_qualified'] * 100, 
                       marker='s', linewidth=2, markersize=4, color='green')
        axes[0, 1].set_title('达标率时间趋势', fontsize=14, fontweight='bold')
        axes[0, 1].set_ylabel('达标率 (%)', fontsize=12)
        axes[0, 1].grid(True, alpha=0.3)
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 主要指标趋势
        indicators = ['do', 'codmn', 'nh3n']
        colors = ['blue', 'red', 'orange']
        
        for i, (indicator, color) in enumerate(zip(indicators, colors)):
            ax = axes[1, 0] if i < 2 else axes[1, 1]
            if i == 2:  # 第三个指标单独一个子图
                ax.clear()
            
            ax.plot(monthly_data.index.astype(str), monthly_data[indicator], 
                   marker='o', linewidth=2, markersize=4, color=color, 
                   label=f'{indicator.upper()}')
            
            if i < 2:
                ax.set_title('溶解氧和高锰酸盐指数趋势', fontsize=14, fontweight='bold')
                ax.set_ylabel('浓度 (mg/L)', fontsize=12)
            else:
                ax.set_title('氨氮浓度趋势', fontsize=14, fontweight='bold')
                ax.set_ylabel('氨氮浓度 (mg/L)', fontsize=12)
            
            ax.grid(True, alpha=0.3)
            ax.tick_params(axis='x', rotation=45)
            ax.legend()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

    def plot_correlation_heatmap(self, df: pd.DataFrame, save_path: Optional[str] = None):
        """
        绘制指标相关性热力图
        
        Args:
            df: 数据框
            save_path: 保存路径
        """
        # 选择数值型指标
        numeric_cols = ['ph', 'do', 'codmn', 'nh3n', 'pollution_index']
        correlation_matrix = df[numeric_cols].corr()
        
        plt.figure(figsize=(10, 8))
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        
        sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='RdYlBu_r', 
                   center=0, square=True, linewidths=0.5, cbar_kws={"shrink": .8})
        
        plt.title('水质指标相关性分析', fontsize=16, fontweight='bold', pad=20)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

    def plot_pollution_source_analysis(self, station_analysis: Dict, save_path: Optional[str] = None):
        """
        绘制污染源分析图
        
        Args:
            station_analysis: 站点分析结果
            save_path: 保存路径
        """
        # 提取各站点的主要污染物信息
        stations = list(station_analysis.keys())
        pollutants = ['pH', '溶解氧', '高锰酸盐指数', '氨氮']
        
        # 创建超标率矩阵
        exceedance_matrix = np.zeros((len(stations), len(pollutants)))
        
        for i, station in enumerate(stations):
            if 'pollution_source_analysis' in station_analysis[station]:
                psa = station_analysis[station]['pollution_source_analysis']
                exceedance_matrix[i, 0] = psa.get('ph_exceedance_rate', 0)
                exceedance_matrix[i, 1] = psa.get('do_exceedance_rate', 0)
                exceedance_matrix[i, 2] = psa.get('codmn_exceedance_rate', 0)
                exceedance_matrix[i, 3] = psa.get('nh3n_exceedance_rate', 0)
        
        plt.figure(figsize=(12, 8))
        sns.heatmap(exceedance_matrix, xticklabels=pollutants, yticklabels=stations,
                   annot=True, fmt='.1f', cmap='Reds', cbar_kws={'label': '超标率 (%)'})
        
        plt.title('各站点主要污染物超标率分析', fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('污染指标', fontsize=12)
        plt.ylabel('监测站点', fontsize=12)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

    def plot_regional_comparison(self, station_analysis: Dict, save_path: Optional[str] = None):
        """
        绘制区域对比图
        
        Args:
            station_analysis: 站点分析结果
            save_path: 保存路径
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        
        # 提取数据
        stations = list(station_analysis.keys())
        qualification_rates = [station_analysis[station]['qualification_rate'] for station in stations]
        pollution_indices = [station_analysis[station]['avg_pollution_index'] for station in stations]
        
        # 达标率对比
        colors1 = ['#2E8B57' if rate >= 80 else '#FFD700' if rate >= 60 else '#DC143C' 
                  for rate in qualification_rates]
        bars1 = ax1.barh(stations, qualification_rates, color=colors1)
        ax1.set_title('各站点水质达标率对比', fontsize=14, fontweight='bold')
        ax1.set_xlabel('达标率 (%)', fontsize=12)
        ax1.axvline(x=80, color='red', linestyle='--', alpha=0.7, label='目标达标率')
        ax1.legend()
        
        # 添加数值标签
        for bar, rate in zip(bars1, qualification_rates):
            ax1.text(bar.get_width() + 1, bar.get_y() + bar.get_height()/2, 
                    f'{rate:.1f}%', va='center', fontsize=10)
        
        # 污染指数对比
        colors2 = ['#2E8B57' if idx <= 1.0 else '#FFD700' if idx <= 2.0 else '#DC143C' 
                  for idx in pollution_indices]
        bars2 = ax2.barh(stations, pollution_indices, color=colors2)
        ax2.set_title('各站点平均污染指数对比', fontsize=14, fontweight='bold')
        ax2.set_xlabel('污染指数', fontsize=12)
        ax2.axvline(x=1.0, color='green', linestyle='--', alpha=0.7, label='清洁标准')
        ax2.axvline(x=2.0, color='orange', linestyle='--', alpha=0.7, label='轻污染标准')
        ax2.legend()
        
        # 添加数值标签
        for bar, idx in zip(bars2, pollution_indices):
            ax2.text(bar.get_width() + 0.05, bar.get_y() + bar.get_height()/2, 
                    f'{idx:.2f}', va='center', fontsize=10)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

    def plot_seasonal_analysis(self, df: pd.DataFrame, save_path: Optional[str] = None):
        """
        绘制季节性分析图
        
        Args:
            df: 数据框
            save_path: 保存路径
        """
        if 'month' not in df.columns:
            print("数据中没有月份信息，无法进行季节性分析")
            return
        
        # 定义季节
        season_map = {12: '冬季', 1: '冬季', 2: '冬季',
                     3: '春季', 4: '春季', 5: '春季',
                     6: '夏季', 7: '夏季', 8: '夏季',
                     9: '秋季', 10: '秋季', 11: '秋季'}
        
        df['season'] = df['month'].map(season_map)
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # 季节性污染指数
        seasonal_pollution = df.groupby('season')['pollution_index'].mean()
        axes[0, 0].bar(seasonal_pollution.index, seasonal_pollution.values, 
                      color=['#87CEEB', '#98FB98', '#F0E68C', '#DDA0DD'])
        axes[0, 0].set_title('季节性污染指数变化', fontsize=14, fontweight='bold')
        axes[0, 0].set_ylabel('平均污染指数', fontsize=12)
        
        # 季节性达标率
        seasonal_qualification = df.groupby('season')['is_qualified'].mean() * 100
        axes[0, 1].bar(seasonal_qualification.index, seasonal_qualification.values,
                      color=['#87CEEB', '#98FB98', '#F0E68C', '#DDA0DD'])
        axes[0, 1].set_title('季节性达标率变化', fontsize=14, fontweight='bold')
        axes[0, 1].set_ylabel('达标率 (%)', fontsize=12)
        
        # 月度变化箱线图
        monthly_data = df.groupby('month')['pollution_index'].apply(list)
        axes[1, 0].boxplot([monthly_data[i] for i in range(1, 13)], 
                          labels=[f'{i}月' for i in range(1, 13)])
        axes[1, 0].set_title('月度污染指数分布', fontsize=14, fontweight='bold')
        axes[1, 0].set_ylabel('污染指数', fontsize=12)
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # 主要指标季节性变化
        seasonal_indicators = df.groupby('season')[['do', 'codmn', 'nh3n']].mean()
        seasonal_indicators.plot(kind='bar', ax=axes[1, 1])
        axes[1, 1].set_title('主要指标季节性变化', fontsize=14, fontweight='bold')
        axes[1, 1].set_ylabel('浓度 (mg/L)', fontsize=12)
        axes[1, 1].legend(['溶解氧', '高锰酸盐指数', '氨氮'])
        axes[1, 1].tick_params(axis='x', rotation=0)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

    def generate_summary_dashboard(self, df: pd.DataFrame, station_analysis: Dict, 
                                 save_path: Optional[str] = None):
        """
        生成综合仪表板
        
        Args:
            df: 数据框
            station_analysis: 站点分析结果
            save_path: 保存路径
        """
        fig = plt.figure(figsize=(20, 12))
        
        # 创建网格布局
        gs = fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)
        
        # 1. 总体水质类别分布饼图
        ax1 = fig.add_subplot(gs[0, 0])
        class_counts = df['water_class'].value_counts()
        colors = [self.colors.get(i, '#808080') for i in class_counts.index]
        labels = [f'{i}类' if i <= 5 else '劣V类' for i in class_counts.index]
        ax1.pie(class_counts.values, labels=labels, colors=colors, autopct='%1.1f%%')
        ax1.set_title('总体水质类别分布', fontsize=12, fontweight='bold')
        
        # 2. 达标率统计
        ax2 = fig.add_subplot(gs[0, 1])
        overall_qualification = df['is_qualified'].mean() * 100
        ax2.text(0.5, 0.7, f'{overall_qualification:.1f}%', ha='center', va='center', 
                fontsize=36, fontweight='bold', color='#2E8B57' if overall_qualification >= 80 else '#DC143C')
        ax2.text(0.5, 0.3, '总体达标率', ha='center', va='center', fontsize=14)
        ax2.set_xlim(0, 1)
        ax2.set_ylim(0, 1)
        ax2.axis('off')
        
        # 3. 平均污染指数
        ax3 = fig.add_subplot(gs[0, 2])
        avg_pollution = df['pollution_index'].mean()
        ax3.text(0.5, 0.7, f'{avg_pollution:.2f}', ha='center', va='center', 
                fontsize=36, fontweight='bold', color='#2E8B57' if avg_pollution <= 1.0 else '#DC143C')
        ax3.text(0.5, 0.3, '平均污染指数', ha='center', va='center', fontsize=14)
        ax3.set_xlim(0, 1)
        ax3.set_ylim(0, 1)
        ax3.axis('off')
        
        # 4. 监测站点数量
        ax4 = fig.add_subplot(gs[0, 3])
        station_count = df['station'].nunique()
        ax4.text(0.5, 0.7, f'{station_count}', ha='center', va='center', 
                fontsize=36, fontweight='bold', color='#4169E1')
        ax4.text(0.5, 0.3, '监测站点数', ha='center', va='center', fontsize=14)
        ax4.set_xlim(0, 1)
        ax4.set_ylim(0, 1)
        ax4.axis('off')
        
        # 5. 各站点达标率条形图
        ax5 = fig.add_subplot(gs[1, :2])
        stations = list(station_analysis.keys())
        qualification_rates = [station_analysis[station]['qualification_rate'] for station in stations]
        colors = ['#2E8B57' if rate >= 80 else '#FFD700' if rate >= 60 else '#DC143C' 
                 for rate in qualification_rates]
        ax5.bar(stations, qualification_rates, color=colors)
        ax5.set_title('各站点达标率', fontsize=12, fontweight='bold')
        ax5.set_ylabel('达标率 (%)')
        ax5.tick_params(axis='x', rotation=45)
        ax5.axhline(y=80, color='red', linestyle='--', alpha=0.7)
        
        # 6. 污染指数分布直方图
        ax6 = fig.add_subplot(gs[1, 2:])
        ax6.hist(df['pollution_index'], bins=20, color='skyblue', alpha=0.7, edgecolor='black')
        ax6.axvline(x=1.0, color='green', linestyle='--', label='清洁标准')
        ax6.axvline(x=2.0, color='orange', linestyle='--', label='轻污染标准')
        ax6.set_title('污染指数分布', fontsize=12, fontweight='bold')
        ax6.set_xlabel('污染指数')
        ax6.set_ylabel('频次')
        ax6.legend()
        
        # 7. 时间趋势（如果有时间数据）
        if 'date' in df.columns:
            ax7 = fig.add_subplot(gs[2, :])
            monthly_data = df.groupby(df['date'].dt.to_period('M')).agg({
                'pollution_index': 'mean',
                'is_qualified': 'mean'
            })
            
            ax7_twin = ax7.twinx()
            line1 = ax7.plot(monthly_data.index.astype(str), monthly_data['pollution_index'], 
                           'b-', marker='o', label='污染指数')
            line2 = ax7_twin.plot(monthly_data.index.astype(str), monthly_data['is_qualified'] * 100, 
                                'r-', marker='s', label='达标率(%)')
            
            ax7.set_title('水质时间趋势', fontsize=12, fontweight='bold')
            ax7.set_ylabel('污染指数', color='b')
            ax7_twin.set_ylabel('达标率 (%)', color='r')
            ax7.tick_params(axis='x', rotation=45)
            
            # 合并图例
            lines = line1 + line2
            labels = [l.get_label() for l in lines]
            ax7.legend(lines, labels, loc='upper left')
        
        plt.suptitle('长江水质综合评价仪表板', fontsize=16, fontweight='bold', y=0.98)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

    def export_analysis_report(self, report: Dict, file_path: str):
        """
        导出分析报告到文本文件
        
        Args:
            report: 综合报告
            file_path: 输出文件路径
        """
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("长江水质综合评价报告\n")
            f.write("=" * 50 + "\n\n")
            
            # 总体评价
            f.write("一、总体评价\n")
            f.write("-" * 20 + "\n")
            summary = report['evaluation_summary']
            f.write(f"监测样本总数：{summary['total_samples']}\n")
            f.write(f"达标样本数：{summary['qualified_samples']}\n")
            f.write(f"总体达标率：{summary['overall_qualification_rate']}%\n")
            f.write(f"平均污染指数：{summary['avg_pollution_index']}\n\n")
            
            # 水质类别分布
            f.write("水质类别分布：\n")
            for class_num, percentage in summary['overall_class_distribution_pct'].items():
                class_name = f"{class_num}类" if class_num <= 5 else "劣V类"
                f.write(f"  {class_name}：{percentage}%\n")
            f.write("\n")
            
            # 站点分析
            f.write("二、各站点水质状况\n")
            f.write("-" * 20 + "\n")
            for station, data in report['station_analysis'].items():
                f.write(f"{station}站：\n")
                f.write(f"  达标率：{data['qualification_rate']}%\n")
                f.write(f"  平均污染指数：{data['avg_pollution_index']}\n")
                f.write(f"  总体评价：{data['overall_assessment']}\n")
                if data['trend_analysis']:
                    f.write(f"  趋势分析：{data['trend_analysis']['trend_description']}\n")
                f.write("\n")
            
            # 建议
            f.write("三、改善建议\n")
            f.write("-" * 20 + "\n")
            for i, recommendation in enumerate(report['recommendations'], 1):
                f.write(f"{i}. {recommendation}\n")
            
        print(f"分析报告已导出到：{file_path}")
