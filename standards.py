import numpy as np
from typing import Dict, List, Tuple
from enum import Enum


class WaterQualityClass(Enum):
    """水质类别枚举"""
    CLASS_I = 1
    CLASS_II = 2
    CLASS_III = 3
    CLASS_IV = 4
    CLASS_V = 5
    INFERIOR_V = 6  # 劣V类


class GB3838Standards:
    """GB3838-2002地表水环境质量标准"""
    
    def __init__(self):
        """初始化水质标准"""
        # pH标准（适用于所有类别）
        self.ph_range = (6.0, 9.0)
        
        # 溶解氧标准 (mg/L)
        self.do_standards = {
            WaterQualityClass.CLASS_I: 7.5,
            WaterQualityClass.CLASS_II: 6.0,
            WaterQualityClass.CLASS_III: 5.0,
            WaterQualityClass.CLASS_IV: 3.0,
            WaterQualityClass.CLASS_V: 2.0
        }
        
        # 高锰酸盐指数标准 (mg/L)
        self.codmn_standards = {
            WaterQualityClass.CLASS_I: 2.0,
            WaterQualityClass.CLASS_II: 4.0,
            WaterQualityClass.CLASS_III: 6.0,
            WaterQualityClass.CLASS_IV: 10.0,
            WaterQualityClass.CLASS_V: 15.0
        }
        
        # 氨氮标准 (mg/L)
        self.nh3n_standards = {
            WaterQualityClass.CLASS_I: 0.15,
            WaterQualityClass.CLASS_II: 0.5,
            WaterQualityClass.CLASS_III: 1.0,
            WaterQualityClass.CLASS_IV: 1.5,
            WaterQualityClass.CLASS_V: 2.0
        }
        
        # 水质类别描述
        self.class_descriptions = {
            WaterQualityClass.CLASS_I: "源头水、国家自然保护区",
            WaterQualityClass.CLASS_II: "集中式生活饮用水地表水源地一级保护区",
            WaterQualityClass.CLASS_III: "集中式生活饮用水地表水源地二级保护区",
            WaterQualityClass.CLASS_IV: "一般工业用水区及人体非直接接触的娱乐用水区",
            WaterQualityClass.CLASS_V: "农业用水区及一般景观要求水域",
            WaterQualityClass.INFERIOR_V: "劣V类水质，污染严重"
        }

    def evaluate_ph(self, ph_value: float) -> bool:
        """
        评价pH值是否符合标准
        
        Args:
            ph_value: pH值
            
        Returns:
            bool: 是否符合标准
        """
        return self.ph_range[0] <= ph_value <= self.ph_range[1]

    def evaluate_do(self, do_value: float) -> WaterQualityClass:
        """
        评价溶解氧水质类别
        
        Args:
            do_value: 溶解氧值 (mg/L)
            
        Returns:
            WaterQualityClass: 水质类别
        """
        if do_value >= self.do_standards[WaterQualityClass.CLASS_I]:
            return WaterQualityClass.CLASS_I
        elif do_value >= self.do_standards[WaterQualityClass.CLASS_II]:
            return WaterQualityClass.CLASS_II
        elif do_value >= self.do_standards[WaterQualityClass.CLASS_III]:
            return WaterQualityClass.CLASS_III
        elif do_value >= self.do_standards[WaterQualityClass.CLASS_IV]:
            return WaterQualityClass.CLASS_IV
        elif do_value >= self.do_standards[WaterQualityClass.CLASS_V]:
            return WaterQualityClass.CLASS_V
        else:
            return WaterQualityClass.INFERIOR_V

    def evaluate_codmn(self, codmn_value: float) -> WaterQualityClass:
        """
        评价高锰酸盐指数水质类别
        
        Args:
            codmn_value: 高锰酸盐指数值 (mg/L)
            
        Returns:
            WaterQualityClass: 水质类别
        """
        if codmn_value <= self.codmn_standards[WaterQualityClass.CLASS_I]:
            return WaterQualityClass.CLASS_I
        elif codmn_value <= self.codmn_standards[WaterQualityClass.CLASS_II]:
            return WaterQualityClass.CLASS_II
        elif codmn_value <= self.codmn_standards[WaterQualityClass.CLASS_III]:
            return WaterQualityClass.CLASS_III
        elif codmn_value <= self.codmn_standards[WaterQualityClass.CLASS_IV]:
            return WaterQualityClass.CLASS_IV
        elif codmn_value <= self.codmn_standards[WaterQualityClass.CLASS_V]:
            return WaterQualityClass.CLASS_V
        else:
            return WaterQualityClass.INFERIOR_V

    def evaluate_nh3n(self, nh3n_value: float) -> WaterQualityClass:
        """
        评价氨氮水质类别
        
        Args:
            nh3n_value: 氨氮值 (mg/L)
            
        Returns:
            WaterQualityClass: 水质类别
        """
        if nh3n_value <= self.nh3n_standards[WaterQualityClass.CLASS_I]:
            return WaterQualityClass.CLASS_I
        elif nh3n_value <= self.nh3n_standards[WaterQualityClass.CLASS_II]:
            return WaterQualityClass.CLASS_II
        elif nh3n_value <= self.nh3n_standards[WaterQualityClass.CLASS_III]:
            return WaterQualityClass.CLASS_III
        elif nh3n_value <= self.nh3n_standards[WaterQualityClass.CLASS_IV]:
            return WaterQualityClass.CLASS_IV
        elif nh3n_value <= self.nh3n_standards[WaterQualityClass.CLASS_V]:
            return WaterQualityClass.CLASS_V
        else:
            return WaterQualityClass.INFERIOR_V

    def comprehensive_evaluation(
        self, 
        ph: float, 
        do: float, 
        codmn: float, 
        nh3n: float
    ) -> Tuple[WaterQualityClass, Dict]:
        """
        综合水质评价（单因子评价法）
        
        Args:
            ph: pH值
            do: 溶解氧值 (mg/L)
            codmn: 高锰酸盐指数值 (mg/L)
            nh3n: 氨氮值 (mg/L)
            
        Returns:
            Tuple[WaterQualityClass, Dict]: 综合水质类别和详细评价结果
        """
        # pH评价
        ph_qualified = self.evaluate_ph(ph)
        
        # 各指标单独评价
        do_class = self.evaluate_do(do)
        codmn_class = self.evaluate_codmn(codmn)
        nh3n_class = self.evaluate_nh3n(nh3n)
        
        # 综合评价：取最差类别（单因子评价法）
        if not ph_qualified:
            comprehensive_class = WaterQualityClass.INFERIOR_V
        else:
            comprehensive_class = max(do_class, codmn_class, nh3n_class, key=lambda x: x.value)
        
        evaluation_details = {
            'ph_qualified': ph_qualified,
            'do_class': do_class,
            'codmn_class': codmn_class,
            'nh3n_class': nh3n_class,
            'comprehensive_class': comprehensive_class,
            'class_description': self.class_descriptions[comprehensive_class]
        }
        
        return comprehensive_class, evaluation_details

    def calculate_pollution_index(
        self, 
        ph: float, 
        do: float, 
        codmn: float, 
        nh3n: float,
        target_class: WaterQualityClass = WaterQualityClass.CLASS_III
    ) -> Dict:
        """
        计算污染指数
        
        Args:
            ph: pH值
            do: 溶解氧值 (mg/L)
            codmn: 高锰酸盐指数值 (mg/L)
            nh3n: 氨氮值 (mg/L)
            target_class: 目标水质类别
            
        Returns:
            Dict: 污染指数结果
        """
        # pH污染指数
        if ph <= 7.0:
            ph_index = (7.0 - ph) / (7.0 - self.ph_range[0])
        else:
            ph_index = (ph - 7.0) / (self.ph_range[1] - 7.0)
        
        # DO污染指数（值越低污染越重）
        do_standard = self.do_standards[target_class]
        do_index = do_standard / do if do > 0 else float('inf')
        
        # CODMn污染指数
        codmn_standard = self.codmn_standards[target_class]
        codmn_index = codmn / codmn_standard
        
        # NH3-N污染指数
        nh3n_standard = self.nh3n_standards[target_class]
        nh3n_index = nh3n / nh3n_standard
        
        # 综合污染指数（算术平均）
        comprehensive_index = (ph_index + do_index + codmn_index + nh3n_index) / 4
        
        return {
            'ph_index': ph_index,
            'do_index': do_index,
            'codmn_index': codmn_index,
            'nh3n_index': nh3n_index,
            'comprehensive_index': comprehensive_index,
            'pollution_level': self._get_pollution_level(comprehensive_index)
        }

    def _get_pollution_level(self, index: float) -> str:
        """
        根据污染指数确定污染程度
        
        Args:
            index: 污染指数
            
        Returns:
            str: 污染程度描述
        """
        if index <= 1.0:
            return "清洁"
        elif index <= 2.0:
            return "轻污染"
        elif index <= 3.0:
            return "中污染"
        else:
            return "重污染"
