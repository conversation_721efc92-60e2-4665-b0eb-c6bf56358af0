#!/usr/bin/env python3
"""
长江水质综合评价分析系统 - 问题(1)专用

功能：
1. 对长江近两年多的水质情况做出定量的综合评价
2. 分析各地区水质的污染状况
3. 生成专业的可视化图表和分析报告

基于GB3838-2002《地表水环境质量标准》进行评价
数据来源：2003年6月-2005年9月长江水质监测数据

作者：长江水质分析系统
日期：2025-07-28
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class YangtzeWaterQualityComprehensiveAnalyzer:
    """长江水质综合评价分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.setup_logging()
        
        # GB3838-2002地表水环境质量标准
        self.standards = {
            'I': {'do': 7.5, 'codmn': 2.0, 'nh3n': 0.15, 'ph_min': 6.0, 'ph_max': 9.0},
            'II': {'do': 6.0, 'codmn': 4.0, 'nh3n': 0.5, 'ph_min': 6.0, 'ph_max': 9.0},
            'III': {'do': 5.0, 'codmn': 6.0, 'nh3n': 1.0, 'ph_min': 6.0, 'ph_max': 9.0},
            'IV': {'do': 3.0, 'codmn': 10.0, 'nh3n': 1.5, 'ph_min': 6.0, 'ph_max': 9.0},
            'V': {'do': 2.0, 'codmn': 15.0, 'nh3n': 2.0, 'ph_min': 6.0, 'ph_max': 9.0}
        }
        
        # 水质类别评分
        self.class_scores = {
            'I': 100, 'II': 80, 'III': 60, 'IV': 40, 'V': 20, '劣V': 0
        }
        
        # 指标权重
        self.weights = {
            'do': 0.25,      # 溶解氧
            'codmn': 0.30,   # 高锰酸盐指数
            'nh3n': 0.30,    # 氨氮
            'ph': 0.15       # pH值
        }
        
        # 污染程度分级
        self.pollution_levels = {
            'excellent': (80, 100, '优秀'),
            'good': (60, 80, '良好'),
            'light': (40, 60, '轻度污染'),
            'moderate': (20, 40, '中度污染'),
            'severe': (0, 20, '重度污染')
        }
        
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.logger.info("长江水质综合评价分析器初始化完成")
    
    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger('yangtze_comprehensive_analyzer')
    
    def load_data(self, file_path: str = 'yangtze_complete_data_2003_2005.csv') -> pd.DataFrame:
        """
        加载水质数据
        
        Args:
            file_path: 数据文件路径
            
        Returns:
            pd.DataFrame: 水质数据
        """
        try:
            df = pd.read_csv(file_path)
            df['date'] = pd.to_datetime(df['date'])
            
            # 数据清洗
            df = df.dropna(subset=['ph', 'do', 'codmn', 'nh3n'])
            
            self.logger.info(f"成功加载水质数据：{len(df)}条记录")
            self.logger.info(f"时间范围：{df['date'].min()} 至 {df['date'].max()}")
            self.logger.info(f"监测站点：{df['station'].nunique()}个")
            
            return df
        except Exception as e:
            self.logger.error(f"数据加载失败：{e}")
            raise
    
    def classify_water_quality(self, row: pd.Series) -> str:
        """
        根据GB3838-2002标准分类水质
        
        Args:
            row: 数据行
            
        Returns:
            str: 水质类别
        """
        ph, do, codmn, nh3n = row['ph'], row['do'], row['codmn'], row['nh3n']
        
        # pH值检查
        if ph < 6.0 or ph > 9.0:
            return '劣V'
        
        # 按最差指标确定类别
        if do < 2.0 or codmn > 15.0 or nh3n > 2.0:
            return '劣V'
        elif do < 3.0 or codmn > 10.0 or nh3n > 1.5:
            return 'V'
        elif do < 5.0 or codmn > 6.0 or nh3n > 1.0:
            return 'IV'
        elif do < 6.0 or codmn > 4.0 or nh3n > 0.5:
            return 'III'
        elif do < 7.5 or codmn > 2.0 or nh3n > 0.15:
            return 'II'
        else:
            return 'I'
    
    def calculate_wqi(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算水质综合指数(WQI)
        
        Args:
            df: 水质数据
            
        Returns:
            pd.DataFrame: 包含WQI的数据
        """
        df = df.copy()
        
        # 重新分类水质
        df['calculated_class'] = df.apply(self.classify_water_quality, axis=1)
        
        # 计算各指标的质量分数
        df['do_score'] = df['calculated_class'].map(self.class_scores)
        df['codmn_score'] = df['calculated_class'].map(self.class_scores)
        df['nh3n_score'] = df['calculated_class'].map(self.class_scores)
        df['ph_score'] = df['calculated_class'].map(self.class_scores)
        
        # 计算WQI
        df['wqi'] = (
            df['do_score'] * self.weights['do'] +
            df['codmn_score'] * self.weights['codmn'] +
            df['nh3n_score'] * self.weights['nh3n'] +
            df['ph_score'] * self.weights['ph']
        )
        
        # 污染程度分级
        def get_pollution_level(wqi):
            for level, (min_val, max_val, name) in self.pollution_levels.items():
                if min_val <= wqi < max_val:
                    return name
            return '重度污染'
        
        df['pollution_level'] = df['wqi'].apply(get_pollution_level)
        
        self.logger.info("WQI计算完成")
        return df
    
    def calculate_pollution_index(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算污染指数
        
        Args:
            df: 水质数据
            
        Returns:
            pd.DataFrame: 包含污染指数的数据
        """
        df = df.copy()
        
        # 以III类标准为基准计算污染指数
        std_iii = self.standards['III']
        
        # DO污染指数（值越小污染越严重，所以用标准值/实测值）
        df['do_pi'] = std_iii['do'] / df['do']
        
        # CODMn和NH3-N污染指数（值越大污染越严重）
        df['codmn_pi'] = df['codmn'] / std_iii['codmn']
        df['nh3n_pi'] = df['nh3n'] / std_iii['nh3n']
        
        # pH污染指数
        df['ph_pi'] = np.where(
            (df['ph'] >= 6.0) & (df['ph'] <= 9.0), 0,
            np.maximum(np.abs(df['ph'] - 6.0), np.abs(df['ph'] - 9.0))
        )
        
        # 综合污染指数
        df['comprehensive_pi'] = (
            df['do_pi'] * self.weights['do'] +
            df['codmn_pi'] * self.weights['codmn'] +
            df['nh3n_pi'] * self.weights['nh3n'] +
            df['ph_pi'] * self.weights['ph']
        )
        
        self.logger.info("污染指数计算完成")
        return df

    def spatial_analysis(self, df: pd.DataFrame) -> Dict:
        """
        空间分布分析

        Args:
            df: 水质数据

        Returns:
            Dict: 空间分析结果
        """
        spatial_stats = {}

        # 按站点统计
        station_stats = df.groupby('station').agg({
            'wqi': ['mean', 'std', 'min', 'max'],
            'comprehensive_pi': ['mean', 'std'],
            'calculated_class': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else 'III'
        }).round(2)

        # 污染程度统计
        pollution_stats = df.groupby('station')['pollution_level'].agg([
            lambda x: (x == '重度污染').sum(),
            lambda x: (x == '中度污染').sum(),
            lambda x: (x == '轻度污染').sum(),
            'count'
        ])
        pollution_stats.columns = ['severe_count', 'moderate_count', 'light_count', 'total_count']
        pollution_stats['severe_rate'] = (pollution_stats['severe_count'] / pollution_stats['total_count'] * 100).round(1)

        # 水质类别分布
        class_distribution = df.groupby('station')['calculated_class'].value_counts().unstack(fill_value=0)
        class_percentage = class_distribution.div(class_distribution.sum(axis=1), axis=0) * 100

        # 主要污染物识别
        pollutant_stats = df.groupby('station').agg({
            'do_pi': 'mean',
            'codmn_pi': 'mean',
            'nh3n_pi': 'mean',
            'ph_pi': 'mean'
        }).round(3)

        # 识别主要污染物
        def identify_main_pollutant(row):
            pollutants = []
            if row['do_pi'] > 1.2:
                pollutants.append('溶解氧')
            if row['codmn_pi'] > 1.2:
                pollutants.append('高锰酸盐指数')
            if row['nh3n_pi'] > 1.2:
                pollutants.append('氨氮')
            if row['ph_pi'] > 0.5:
                pollutants.append('pH')
            return ', '.join(pollutants) if pollutants else '无明显污染'

        pollutant_stats['main_pollutants'] = pollutant_stats.apply(identify_main_pollutant, axis=1)

        spatial_stats = {
            'station_stats': station_stats,
            'pollution_stats': pollution_stats,
            'class_distribution': class_distribution,
            'class_percentage': class_percentage,
            'pollutant_stats': pollutant_stats
        }

        self.logger.info("空间分析完成")
        return spatial_stats

    def temporal_analysis(self, df: pd.DataFrame) -> Dict:
        """
        时间趋势分析

        Args:
            df: 水质数据

        Returns:
            Dict: 时间分析结果
        """
        temporal_stats = {}

        # 按月份统计
        df['year_month'] = df['date'].dt.to_period('M')
        monthly_stats = df.groupby('year_month').agg({
            'wqi': ['mean', 'std'],
            'comprehensive_pi': 'mean'
        }).round(2)

        # 单独计算主要水质类别
        monthly_class = df.groupby('year_month')['calculated_class'].apply(
            lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else 'III'
        )

        # 季节性分析
        df['season'] = df['date'].dt.month.map({
            12: '冬季', 1: '冬季', 2: '冬季',
            3: '春季', 4: '春季', 5: '春季',
            6: '夏季', 7: '夏季', 8: '夏季',
            9: '秋季', 10: '秋季', 11: '秋季'
        })

        seasonal_stats = df.groupby('season').agg({
            'wqi': ['mean', 'std'],
            'comprehensive_pi': 'mean',
            'do': 'mean',
            'codmn': 'mean',
            'nh3n': 'mean'
        }).round(2)

        # 年度趋势
        df['year'] = df['date'].dt.year
        yearly_stats = df.groupby('year').agg({
            'wqi': ['mean', 'std'],
            'comprehensive_pi': 'mean'
        }).round(2)

        # 污染恶化/改善趋势
        monthly_wqi = df.groupby('year_month')['wqi'].mean()
        trend_slope = np.polyfit(range(len(monthly_wqi)), monthly_wqi.values, 1)[0]
        trend_direction = '改善' if trend_slope > 0 else '恶化' if trend_slope < 0 else '稳定'

        temporal_stats = {
            'monthly_stats': monthly_stats,
            'monthly_class': monthly_class,
            'seasonal_stats': seasonal_stats,
            'yearly_stats': yearly_stats,
            'trend_slope': trend_slope,
            'trend_direction': trend_direction,
            'monthly_wqi': monthly_wqi
        }

        self.logger.info("时间分析完成")
        return temporal_stats

    def create_comprehensive_visualizations(self, df: pd.DataFrame, spatial_stats: Dict, temporal_stats: Dict):
        """
        创建综合可视化图表

        Args:
            df: 水质数据
            spatial_stats: 空间分析结果
            temporal_stats: 时间分析结果
        """
        # 设置图表样式
        plt.style.use('seaborn-v0_8')

        # 1. 水质综合评价时间序列图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('长江水质综合评价分析 - 问题(1)', fontsize=16, fontweight='bold')

        # 1.1 WQI时间趋势
        monthly_wqi = temporal_stats['monthly_wqi']
        axes[0, 0].plot(monthly_wqi.index.astype(str), monthly_wqi.values,
                       marker='o', linewidth=2, markersize=4)
        axes[0, 0].set_title('水质综合指数(WQI)时间变化趋势', fontsize=12, fontweight='bold')
        axes[0, 0].set_xlabel('时间')
        axes[0, 0].set_ylabel('WQI值')
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].tick_params(axis='x', rotation=45)

        # 添加趋势线
        x_numeric = range(len(monthly_wqi))
        z = np.polyfit(x_numeric, monthly_wqi.values, 1)
        p = np.poly1d(z)
        axes[0, 0].plot(monthly_wqi.index.astype(str), p(x_numeric),
                       "r--", alpha=0.8, linewidth=2,
                       label=f'趋势: {temporal_stats["trend_direction"]}')
        axes[0, 0].legend()

        # 1.2 各站点WQI对比
        station_wqi = spatial_stats['station_stats']['wqi']['mean'].sort_values()
        colors = ['red' if x < 40 else 'orange' if x < 60 else 'yellow' if x < 80 else 'green'
                 for x in station_wqi.values]

        bars = axes[0, 1].barh(range(len(station_wqi)), station_wqi.values, color=colors, alpha=0.7)
        axes[0, 1].set_yticks(range(len(station_wqi)))
        axes[0, 1].set_yticklabels([s[:10] + '...' if len(s) > 10 else s for s in station_wqi.index], fontsize=9)
        axes[0, 1].set_title('各地区水质综合指数(WQI)对比', fontsize=12, fontweight='bold')
        axes[0, 1].set_xlabel('WQI值')
        axes[0, 1].grid(True, alpha=0.3, axis='x')

        # 添加数值标签
        for i, (bar, value) in enumerate(zip(bars, station_wqi.values)):
            axes[0, 1].text(value + 1, i, f'{value:.1f}',
                           va='center', fontsize=9)

        # 1.3 水质类别分布
        class_counts = df['calculated_class'].value_counts()
        colors_pie = ['green', 'lightgreen', 'yellow', 'orange', 'red', 'darkred']
        wedges, texts, autotexts = axes[1, 0].pie(class_counts.values,
                                                 labels=class_counts.index,
                                                 colors=colors_pie[:len(class_counts)],
                                                 autopct='%1.1f%%',
                                                 startangle=90)
        axes[1, 0].set_title('水质类别分布', fontsize=12, fontweight='bold')

        # 1.4 主要污染物污染指数对比
        pollutant_means = df[['do_pi', 'codmn_pi', 'nh3n_pi']].mean()
        pollutant_names = ['溶解氧', '高锰酸盐指数', '氨氮']
        colors_bar = ['skyblue', 'lightcoral', 'lightgreen']

        bars = axes[1, 1].bar(pollutant_names, pollutant_means.values,
                             color=colors_bar, alpha=0.7)
        axes[1, 1].set_title('主要污染物污染指数对比', fontsize=12, fontweight='bold')
        axes[1, 1].set_ylabel('污染指数')
        axes[1, 1].axhline(y=1.0, color='red', linestyle='--', alpha=0.7, label='标准线(1.0)')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3, axis='y')

        # 添加数值标签
        for bar, value in zip(bars, pollutant_means.values):
            axes[1, 1].text(bar.get_x() + bar.get_width()/2, value + 0.02,
                           f'{value:.2f}', ha='center', va='bottom', fontsize=10)

        plt.tight_layout()

        # 保存图表
        filename = f'问题1_水质综合评价_{self.timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        self.logger.info(f"综合评价图表已保存：{filename}")
        plt.show()

        # 2. 污染状况详细分析图
        self._create_pollution_analysis_chart(df, spatial_stats)

        # 3. 时空分布热力图
        self._create_spatiotemporal_heatmap(df)

    def _create_pollution_analysis_chart(self, df: pd.DataFrame, spatial_stats: Dict):
        """创建污染状况分析图表"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('长江各地区污染状况详细分析 - 问题(1)', fontsize=16, fontweight='bold')

        # 2.1 各站点污染程度分布
        pollution_data = spatial_stats['pollution_stats']
        stations = pollution_data.index
        severe_rates = pollution_data['severe_rate'].values

        colors = ['darkred' if x > 20 else 'red' if x > 10 else 'orange' if x > 5 else 'green'
                 for x in severe_rates]

        bars = axes[0, 0].barh(range(len(stations)), severe_rates, color=colors, alpha=0.7)
        axes[0, 0].set_yticks(range(len(stations)))
        axes[0, 0].set_yticklabels([s[:10] + '...' if len(s) > 10 else s for s in stations], fontsize=9)
        axes[0, 0].set_title('各地区重度污染比例', fontsize=12, fontweight='bold')
        axes[0, 0].set_xlabel('重度污染比例 (%)')
        axes[0, 0].grid(True, alpha=0.3, axis='x')

        # 添加数值标签
        for i, (bar, value) in enumerate(zip(bars, severe_rates)):
            axes[0, 0].text(value + 0.5, i, f'{value:.1f}%',
                           va='center', fontsize=9)

        # 2.2 季节性污染特征
        seasonal_data = temporal_stats['seasonal_stats']['wqi']['mean']
        seasons = seasonal_data.index
        wqi_values = seasonal_data.values

        colors_season = ['lightblue', 'lightgreen', 'orange', 'brown']
        bars = axes[0, 1].bar(seasons, wqi_values, color=colors_season, alpha=0.7)
        axes[0, 1].set_title('季节性水质变化', fontsize=12, fontweight='bold')
        axes[0, 1].set_ylabel('平均WQI值')
        axes[0, 1].grid(True, alpha=0.3, axis='y')

        # 添加数值标签
        for bar, value in zip(bars, wqi_values):
            axes[0, 1].text(bar.get_x() + bar.get_width()/2, value + 1,
                           f'{value:.1f}', ha='center', va='bottom', fontsize=10)

        # 2.3 污染物浓度箱线图
        pollutants_data = df[['codmn', 'nh3n']].melt(var_name='pollutant', value_name='concentration')
        pollutants_data['pollutant'] = pollutants_data['pollutant'].map({
            'codmn': '高锰酸盐指数', 'nh3n': '氨氮'
        })

        sns.boxplot(data=pollutants_data, x='pollutant', y='concentration', ax=axes[1, 0])
        axes[1, 0].set_title('主要污染物浓度分布', fontsize=12, fontweight='bold')
        axes[1, 0].set_xlabel('污染物')
        axes[1, 0].set_ylabel('浓度 (mg/L)')
        axes[1, 0].grid(True, alpha=0.3, axis='y')

        # 2.4 水质类别时间变化
        class_time = df.groupby([df['date'].dt.to_period('M'), 'calculated_class']).size().unstack(fill_value=0)
        class_time_pct = class_time.div(class_time.sum(axis=1), axis=0) * 100

        # 选择主要类别绘制
        main_classes = ['III', 'IV', 'V', '劣V']
        available_classes = [cls for cls in main_classes if cls in class_time_pct.columns]

        for i, cls in enumerate(available_classes):
            axes[1, 1].plot(class_time_pct.index.astype(str), class_time_pct[cls],
                           marker='o', label=f'{cls}类', linewidth=2, markersize=3)

        axes[1, 1].set_title('水质类别时间变化趋势', fontsize=12, fontweight='bold')
        axes[1, 1].set_xlabel('时间')
        axes[1, 1].set_ylabel('比例 (%)')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        axes[1, 1].tick_params(axis='x', rotation=45)

        plt.tight_layout()

        # 保存图表
        filename = f'问题1_污染状况分析_{self.timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        self.logger.info(f"污染状况分析图表已保存：{filename}")
        plt.show()

    def _create_spatiotemporal_heatmap(self, df: pd.DataFrame):
        """创建时空分布热力图"""
        fig, axes = plt.subplots(1, 2, figsize=(16, 8))
        fig.suptitle('长江水质时空分布热力图 - 问题(1)', fontsize=16, fontweight='bold')

        # 3.1 WQI时空分布热力图
        df['year_month'] = df['date'].dt.to_period('M')
        wqi_pivot = df.pivot_table(values='wqi', index='station', columns='year_month', aggfunc='mean')

        sns.heatmap(wqi_pivot, annot=False, cmap='RdYlGn', center=50,
                   cbar_kws={'label': 'WQI值'}, ax=axes[0])
        axes[0].set_title('水质综合指数(WQI)时空分布', fontsize=12, fontweight='bold')
        axes[0].set_xlabel('时间')
        axes[0].set_ylabel('监测站点')
        axes[0].tick_params(axis='x', rotation=45)

        # 3.2 综合污染指数时空分布热力图
        pi_pivot = df.pivot_table(values='comprehensive_pi', index='station', columns='year_month', aggfunc='mean')

        sns.heatmap(pi_pivot, annot=False, cmap='YlOrRd', center=1.0,
                   cbar_kws={'label': '污染指数'}, ax=axes[1])
        axes[1].set_title('综合污染指数时空分布', fontsize=12, fontweight='bold')
        axes[1].set_xlabel('时间')
        axes[1].set_ylabel('监测站点')
        axes[1].tick_params(axis='x', rotation=45)

        plt.tight_layout()

        # 保存图表
        filename = f'问题1_时空分布分析_{self.timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        self.logger.info(f"时空分布分析图表已保存：{filename}")
        plt.show()

    def generate_comprehensive_report(self, df: pd.DataFrame, spatial_stats: Dict, temporal_stats: Dict) -> str:
        """
        生成综合评价报告

        Args:
            df: 水质数据
            spatial_stats: 空间分析结果
            temporal_stats: 时间分析结果

        Returns:
            str: 报告内容
        """
        report = []
        report.append("=" * 80)
        report.append("长江水质综合评价分析报告 - 问题(1)")
        report.append("=" * 80)
        report.append(f"分析时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}")
        report.append(f"数据来源：长江水质监测数据（2003年6月-2005年9月）")
        report.append(f"评价标准：GB3838-2002《地表水环境质量标准》")
        report.append("")

        # 1. 数据概况
        report.append("一、数据概况")
        report.append("-" * 40)
        report.append(f"• 监测记录总数：{len(df):,}条")
        report.append(f"• 监测站点数量：{df['station'].nunique()}个")
        report.append(f"• 监测时间跨度：{df['date'].min().strftime('%Y年%m月')} - {df['date'].max().strftime('%Y年%m月')}")
        report.append(f"• 监测指标：pH、溶解氧(DO)、高锰酸盐指数(CODMn)、氨氮(NH3-N)")
        report.append("")

        # 2. 定量综合评价
        report.append("二、定量综合评价结果")
        report.append("-" * 40)

        # 整体水质状况
        overall_wqi = df['wqi'].mean()
        overall_level = df['pollution_level'].mode().iloc[0] if len(df['pollution_level'].mode()) > 0 else '中度污染'

        report.append(f"• 长江整体水质综合指数(WQI)：{overall_wqi:.1f}")
        report.append(f"• 整体污染程度：{overall_level}")

        # 水质类别分布
        class_dist = df['calculated_class'].value_counts(normalize=True) * 100
        report.append(f"• 水质类别分布：")
        for cls in ['I', 'II', 'III', 'IV', 'V', '劣V']:
            if cls in class_dist.index:
                report.append(f"  - {cls}类水：{class_dist[cls]:.1f}%")

        # 可饮用水比例
        drinkable_classes = ['I', 'II', 'III']
        drinkable_rate = class_dist[class_dist.index.isin(drinkable_classes)].sum()
        report.append(f"• 可饮用水比例（I-III类）：{drinkable_rate:.1f}%")

        # 污染水比例
        polluted_classes = ['IV', 'V', '劣V']
        polluted_rate = class_dist[class_dist.index.isin(polluted_classes)].sum()
        report.append(f"• 污染水比例（IV类及以下）：{polluted_rate:.1f}%")
        report.append("")

        # 3. 各地区污染状况分析
        report.append("三、各地区污染状况分析")
        report.append("-" * 40)

        # 按WQI排序
        station_wqi = spatial_stats['station_stats']['wqi']['mean'].sort_values(ascending=False)
        pollution_stats = spatial_stats['pollution_stats']

        report.append("• 各地区水质综合指数(WQI)排名：")
        for i, (station, wqi) in enumerate(station_wqi.items(), 1):
            level = '优秀' if wqi >= 80 else '良好' if wqi >= 60 else '轻度污染' if wqi >= 40 else '中度污染' if wqi >= 20 else '重度污染'
            severe_rate = pollution_stats.loc[station, 'severe_rate']
            report.append(f"  {i:2d}. {station:<12} WQI={wqi:5.1f} ({level}) 重度污染率={severe_rate:4.1f}%")

        report.append("")

        # 污染热点地区
        severe_pollution = pollution_stats[pollution_stats['severe_rate'] > 10].sort_values('severe_rate', ascending=False)
        if len(severe_pollution) > 0:
            report.append("• 重点关注地区（重度污染率>10%）：")
            for station, data in severe_pollution.iterrows():
                main_pollutants = spatial_stats['pollutant_stats'].loc[station, 'main_pollutants']
                report.append(f"  - {station}：重度污染率{data['severe_rate']:.1f}%，主要污染物：{main_pollutants}")
        else:
            report.append("• 无重度污染率超过10%的地区")

        report.append("")

        # 4. 主要污染物分析
        report.append("四、主要污染物分析")
        report.append("-" * 40)

        pollutant_means = df[['do_pi', 'codmn_pi', 'nh3n_pi']].mean()
        pollutant_names = {'do_pi': '溶解氧', 'codmn_pi': '高锰酸盐指数', 'nh3n_pi': '氨氮'}

        report.append("• 各污染物平均污染指数：")
        for pollutant, pi_value in pollutant_means.items():
            name = pollutant_names[pollutant]
            status = '超标' if pi_value > 1.0 else '达标'
            report.append(f"  - {name}：{pi_value:.2f} ({status})")

        # 超标率统计
        report.append("")
        report.append("• 各污染物超标率（以III类标准为基准）：")
        do_exceed = (df['do'] < 5.0).mean() * 100
        codmn_exceed = (df['codmn'] > 6.0).mean() * 100
        nh3n_exceed = (df['nh3n'] > 1.0).mean() * 100
        ph_exceed = ((df['ph'] < 6.0) | (df['ph'] > 9.0)).mean() * 100

        report.append(f"  - 溶解氧超标率：{do_exceed:.1f}%")
        report.append(f"  - 高锰酸盐指数超标率：{codmn_exceed:.1f}%")
        report.append(f"  - 氨氮超标率：{nh3n_exceed:.1f}%")
        report.append(f"  - pH超标率：{ph_exceed:.1f}%")
        report.append("")

        # 5. 时间变化趋势
        report.append("五、时间变化趋势分析")
        report.append("-" * 40)

        trend_direction = temporal_stats['trend_direction']
        trend_slope = temporal_stats['trend_slope']

        report.append(f"• 整体趋势：水质呈{trend_direction}趋势")
        report.append(f"• 趋势斜率：{trend_slope:.3f} (WQI单位/月)")

        # 季节性特征
        seasonal_wqi = temporal_stats['seasonal_stats']['wqi']['mean']
        best_season = seasonal_wqi.idxmax()
        worst_season = seasonal_wqi.idxmin()

        report.append(f"• 季节性特征：")
        report.append(f"  - 水质最好季节：{best_season}（WQI={seasonal_wqi[best_season]:.1f}）")
        report.append(f"  - 水质最差季节：{worst_season}（WQI={seasonal_wqi[worst_season]:.1f}）")

        for season, wqi in seasonal_wqi.items():
            report.append(f"  - {season}：WQI={wqi:.1f}")

        report.append("")

        # 6. 主要结论
        report.append("六、主要结论与建议")
        report.append("-" * 40)

        if overall_wqi >= 60:
            conclusion = "长江整体水质状况良好"
        elif overall_wqi >= 40:
            conclusion = "长江整体水质存在轻度污染"
        elif overall_wqi >= 20:
            conclusion = "长江整体水质存在中度污染，需要重点治理"
        else:
            conclusion = "长江整体水质存在重度污染，情况严峻"

        report.append(f"• 总体评价：{conclusion}")

        # 重点问题
        if polluted_rate > 50:
            report.append(f"• 重点问题：污染水比例高达{polluted_rate:.1f}%，超过一半监测点水质不达标")

        if len(severe_pollution) > 0:
            report.append(f"• 重点关注：{len(severe_pollution)}个地区重度污染率超过10%，需要优先治理")

        # 主要污染源
        main_pollutant = pollutant_means.idxmax()
        main_pollutant_name = pollutant_names[main_pollutant]
        report.append(f"• 主要污染源：{main_pollutant_name}是当前最主要的污染因子")

        report.append("")
        report.append("=" * 80)
        report.append(f"报告生成时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}")
        report.append("=" * 80)

        return "\n".join(report)

    def run_comprehensive_analysis(self) -> Dict:
        """
        运行完整的综合分析

        Returns:
            Dict: 分析结果
        """
        self.logger.info("开始长江水质综合评价分析...")

        # 1. 加载数据
        df = self.load_data()

        # 2. 计算综合指标
        df = self.calculate_wqi(df)
        df = self.calculate_pollution_index(df)

        # 3. 空间分析
        spatial_stats = self.spatial_analysis(df)

        # 4. 时间分析
        temporal_stats = self.temporal_analysis(df)

        # 5. 生成可视化
        self.create_comprehensive_visualizations(df, spatial_stats, temporal_stats)

        # 6. 生成报告
        report = self.generate_comprehensive_report(df, spatial_stats, temporal_stats)

        # 保存报告
        report_filename = f'问题1_长江水质综合评价报告_{self.timestamp}.txt'
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report)

        self.logger.info(f"综合评价报告已保存：{report_filename}")

        # 保存分析数据
        analysis_data = df[['date', 'station', 'wqi', 'pollution_level', 'comprehensive_pi',
                           'calculated_class', 'do_pi', 'codmn_pi', 'nh3n_pi']].copy()
        data_filename = f'问题1_评价结果数据_{self.timestamp}.csv'
        analysis_data.to_csv(data_filename, index=False, encoding='utf-8-sig')

        self.logger.info(f"评价结果数据已保存：{data_filename}")

        results = {
            'dataframe': df,
            'spatial_stats': spatial_stats,
            'temporal_stats': temporal_stats,
            'report': report,
            'overall_wqi': df['wqi'].mean(),
            'pollution_rate': (df['calculated_class'].isin(['IV', 'V', '劣V'])).mean() * 100
        }

        self.logger.info("长江水质综合评价分析完成！")
        return results


if __name__ == "__main__":
    # 运行综合分析
    analyzer = YangtzeWaterQualityComprehensiveAnalyzer()
    results = analyzer.run_comprehensive_analysis()

    print("\n" + "=" * 80)
    print("长江水质综合评价分析 - 问题(1) 完成")
    print("=" * 80)
    print(f"整体水质综合指数(WQI)：{results['overall_wqi']:.1f}")
    print(f"污染水比例：{results['pollution_rate']:.1f}%")
    print("详细分析结果请查看生成的图表和报告文件。")
    print("=" * 80)
