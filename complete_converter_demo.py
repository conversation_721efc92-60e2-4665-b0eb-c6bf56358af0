#!/usr/bin/env python3
"""
完整长江水质数据转换器演示程序
展示扩展后的数据转换和分析功能（2003年6月-2004年9月，272条记录）
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from complete_data_converter import FixedCompleteYangtzeDataConverter
import numpy as np

def main():
    """主演示程序"""
    print("="*80)
    print("完整长江水质数据转换器演示程序")
    print("数据范围：2003年6月-2005年9月（476条记录）")
    print("="*80)
    
    # 初始化转换器
    converter = FixedCompleteYangtzeDataConverter()
    
    # 解析完整数据
    print("\n1. 解析完整真实数据...")
    df = converter.parse_user_real_data()
    
    # 导出CSV
    print("\n2. 导出CSV文件...")
    csv_file = converter.export_to_csv(df)
    
    # 生成分析摘要
    print("\n3. 生成分析摘要...")
    summary = converter.generate_analysis_summary(df)
    
    # 保存分析摘要
    with open("complete_data_analysis_2003_2005.txt", "w", encoding="utf-8") as f:
        f.write(summary)
    
    print(summary)
    
    # 数据统计分析
    print("\n4. 详细数据统计分析...")
    perform_detailed_analysis(df)
    
    # 污染趋势分析
    print("\n5. 污染趋势分析...")
    analyze_pollution_trends(df)
    
    print(f"\n{'='*80}")
    print("演示完成！生成的文件：")
    print(f"1. CSV数据文件：{csv_file}")
    print(f"2. 分析摘要：complete_data_analysis_2003_2005.txt")
    print(f"3. 详细统计：detailed_statistics.txt")
    print(f"4. 污染趋势：pollution_trends.txt")
    print(f"{'='*80}")

def perform_detailed_analysis(df):
    """执行详细的数据统计分析"""
    
    # 基本统计信息
    stats_summary = f"""
详细数据统计分析报告
{'='*50}

1. 基本统计信息：
总记录数：{len(df)}条
时间跨度：{df['date'].min()} 至 {df['date'].max()}
监测站点：{df['station'].nunique()}个
数据完整性：{len(df)/476*100:.1f}%

2. 水质指标统计：
pH值：
  - 平均值：{df['ph'].mean():.2f}
  - 标准差：{df['ph'].std():.2f}
  - 范围：{df['ph'].min():.2f} - {df['ph'].max():.2f}

溶解氧(DO)：
  - 平均值：{df['do'].mean():.2f} mg/L
  - 标准差：{df['do'].std():.2f} mg/L
  - 范围：{df['do'].min():.2f} - {df['do'].max():.2f} mg/L

高锰酸盐指数(CODMn)：
  - 平均值：{df['codmn'].mean():.2f} mg/L
  - 标准差：{df['codmn'].std():.2f} mg/L
  - 范围：{df['codmn'].min():.2f} - {df['codmn'].max():.2f} mg/L

氨氮(NH3-N)：
  - 平均值：{df['nh3n'].mean():.2f} mg/L
  - 标准差：{df['nh3n'].std():.2f} mg/L
  - 范围：{df['nh3n'].min():.2f} - {df['nh3n'].max():.2f} mg/L

3. 水质类别分布：
"""
    
    # 水质类别统计
    class_counts = df['water_class_current'].value_counts()
    for class_name, count in class_counts.items():
        percentage = count / len(df) * 100
        stats_summary += f"  - {class_name}类：{count}次 ({percentage:.1f}%)\n"
    
    # 站点污染统计
    stats_summary += f"""
4. 站点污染统计：
最清洁站点（I类水质最多）：
"""
    clean_stations = df[df['water_class_current'] == 'I']['station'].value_counts().head(3)
    for station, count in clean_stations.items():
        stats_summary += f"  - {station}：{count}次\n"
    
    stats_summary += f"""
最污染站点（劣V类水质最多）：
"""
    polluted_stations = df[df['water_class_current'] == '劣V']['station'].value_counts().head(3)
    for station, count in polluted_stations.items():
        stats_summary += f"  - {station}：{count}次\n"
    
    # 保存详细统计
    with open("detailed_statistics.txt", "w", encoding="utf-8") as f:
        f.write(stats_summary)
    
    print("详细统计分析完成，已保存到 detailed_statistics.txt")

def analyze_pollution_trends(df):
    """分析污染趋势"""
    
    # 按月份分析水质变化
    monthly_stats = df.groupby('date').agg({
        'water_class_current': lambda x: (x == '劣V').sum(),  # 劣V类次数
        'nh3n': 'mean',  # 平均氨氮浓度
        'do': 'mean',    # 平均溶解氧
        'codmn': 'mean'  # 平均高锰酸盐指数
    }).round(2)
    
    trends_summary = f"""
污染趋势分析报告
{'='*50}

1. 月度污染趋势：
"""
    
    for date, row in monthly_stats.iterrows():
        trends_summary += f"""
{date.strftime('%Y年%m月')}：
  - 劣V类水质次数：{row['water_class_current']}次
  - 平均氨氮浓度：{row['nh3n']:.2f} mg/L
  - 平均溶解氧：{row['do']:.2f} mg/L
  - 平均高锰酸盐指数：{row['codmn']:.2f} mg/L
"""
    
    # 季节性分析
    df['season'] = df['date'].dt.month.map({
        6: '夏季', 7: '夏季', 8: '夏季',
        9: '秋季', 10: '秋季', 11: '秋季',
        12: '冬季', 1: '冬季', 2: '冬季',
        3: '春季', 4: '春季', 5: '春季'
    })
    
    seasonal_pollution = df.groupby('season').agg({
        'water_class_current': lambda x: (x == '劣V').sum(),
        'nh3n': 'mean'
    }).round(2)
    
    trends_summary += f"""
2. 季节性污染特征：
"""
    for season, row in seasonal_pollution.iterrows():
        trends_summary += f"  - {season}：劣V类{row['water_class_current']}次，平均氨氮{row['nh3n']:.2f} mg/L\n"
    
    # 重点污染站点趋势
    key_polluted_stations = ['江西南昌滁槎', '四川泸州沱江二桥', '四川乐山岷江大桥']
    trends_summary += f"""
3. 重点污染站点趋势：
"""
    
    for station in key_polluted_stations:
        station_data = df[df['station'] == station]
        if len(station_data) > 0:
            worst_class_count = (station_data['water_class_current'] == '劣V').sum()
            avg_nh3n = station_data['nh3n'].mean()
            max_nh3n = station_data['nh3n'].max()
            trends_summary += f"""
{station}：
  - 劣V类水质次数：{worst_class_count}次
  - 平均氨氮浓度：{avg_nh3n:.2f} mg/L
  - 最高氨氮浓度：{max_nh3n:.2f} mg/L
"""
    
    # 保存趋势分析
    with open("pollution_trends.txt", "w", encoding="utf-8") as f:
        f.write(trends_summary)
    
    print("污染趋势分析完成，已保存到 pollution_trends.txt")

if __name__ == "__main__":
    main()
