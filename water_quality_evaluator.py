"""
长江水质综合评价器
实现水质评价的核心功能
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional
from datetime import datetime

from standards import GB3838Standards, WaterQualityClass
from data_processor import WaterQualityDataProcessor


class WaterQualityEvaluator:
    """水质综合评价器"""
    
    def __init__(self):
        """初始化评价器"""
        self.standards = GB3838Standards()
        self.data_processor = WaterQualityDataProcessor()
        self.logger = self._setup_logger()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger

    def evaluate_single_sample(
        self, 
        ph: float, 
        do: float, 
        codmn: float, 
        nh3n: float
    ) -> Dict:
        """
        评价单个水样
        
        Args:
            ph: pH值
            do: 溶解氧值 (mg/L)
            codmn: 高锰酸盐指数值 (mg/L)
            nh3n: 氨氮值 (mg/L)
            
        Returns:
            Dict: 评价结果
        """
        # 标准评价
        comprehensive_class, evaluation_details = self.standards.comprehensive_evaluation(
            ph, do, codmn, nh3n
        )
        
        # 污染指数计算
        pollution_indices = self.standards.calculate_pollution_index(
            ph, do, codmn, nh3n
        )
        
        result = {
            'water_quality_class': comprehensive_class,
            'class_description': evaluation_details['class_description'],
            'evaluation_details': evaluation_details,
            'pollution_indices': pollution_indices,
            'is_qualified': comprehensive_class.value <= WaterQualityClass.CLASS_III.value
        }
        
        return result

    def evaluate_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        评价整个数据框
        
        Args:
            df: 包含水质数据的数据框
            
        Returns:
            pd.DataFrame: 包含评价结果的数据框
        """
        result_df = df.copy()
        
        # 批量评价
        evaluation_results = []
        pollution_indices = []
        water_classes = []
        is_qualified = []
        
        for _, row in df.iterrows():
            result = self.evaluate_single_sample(
                row['ph'], row['do'], row['codmn'], row['nh3n']
            )
            
            evaluation_results.append(result['evaluation_details'])
            pollution_indices.append(result['pollution_indices']['comprehensive_index'])
            water_classes.append(result['water_quality_class'].value)
            is_qualified.append(result['is_qualified'])
        
        # 添加评价结果到数据框
        result_df['water_class'] = water_classes
        result_df['pollution_index'] = pollution_indices
        result_df['is_qualified'] = is_qualified
        
        # 添加水质类别描述
        result_df['class_description'] = result_df['water_class'].apply(
            lambda x: self.standards.class_descriptions[WaterQualityClass(x)]
        )
        
        return result_df

    def analyze_station_quality(self, df: pd.DataFrame) -> Dict:
        """
        分析各站点水质状况
        
        Args:
            df: 评价后的数据框
            
        Returns:
            Dict: 站点水质分析结果
        """
        station_analysis = {}
        
        for station in df['station'].unique():
            station_data = df[df['station'] == station]
            
            # 基本统计
            total_samples = len(station_data)
            qualified_samples = station_data['is_qualified'].sum()
            qualification_rate = qualified_samples / total_samples * 100
            
            # 水质类别分布
            class_distribution = station_data['water_class'].value_counts().to_dict()
            class_distribution_pct = (station_data['water_class'].value_counts(normalize=True) * 100).to_dict()
            
            # 平均污染指数
            avg_pollution_index = station_data['pollution_index'].mean()
            
            # 各指标平均值
            avg_indicators = {
                'ph': station_data['ph'].mean(),
                'do': station_data['do'].mean(),
                'codmn': station_data['codmn'].mean(),
                'nh3n': station_data['nh3n'].mean()
            }
            
            # 趋势分析（如果有时间数据）
            trend_analysis = None
            if 'date' in station_data.columns:
                trend_analysis = self._analyze_trend(station_data)
            
            station_analysis[station] = {
                'total_samples': total_samples,
                'qualified_samples': qualified_samples,
                'qualification_rate': round(qualification_rate, 2),
                'class_distribution': class_distribution,
                'class_distribution_pct': {k: round(v, 2) for k, v in class_distribution_pct.items()},
                'avg_pollution_index': round(avg_pollution_index, 3),
                'avg_indicators': {k: round(v, 3) for k, v in avg_indicators.items()},
                'trend_analysis': trend_analysis,
                'overall_assessment': self._get_overall_assessment(qualification_rate, avg_pollution_index)
            }
        
        return station_analysis

    def _analyze_trend(self, station_data: pd.DataFrame) -> Dict:
        """
        分析站点水质趋势
        
        Args:
            station_data: 站点数据
            
        Returns:
            Dict: 趋势分析结果
        """
        # 按年度分析
        yearly_stats = station_data.groupby('year').agg({
            'pollution_index': 'mean',
            'is_qualified': 'mean',
            'ph': 'mean',
            'do': 'mean',
            'codmn': 'mean',
            'nh3n': 'mean'
        }).round(3)
        
        # 计算趋势（简单线性回归斜率）
        years = yearly_stats.index.values
        if len(years) > 1:
            pollution_trend = np.polyfit(years, yearly_stats['pollution_index'], 1)[0]
            qualification_trend = np.polyfit(years, yearly_stats['is_qualified'], 1)[0]
        else:
            pollution_trend = 0
            qualification_trend = 0
        
        return {
            'yearly_stats': yearly_stats.to_dict(),
            'pollution_trend': round(pollution_trend, 4),
            'qualification_trend': round(qualification_trend, 4),
            'trend_description': self._describe_trend(pollution_trend, qualification_trend)
        }

    def _describe_trend(self, pollution_trend: float, qualification_trend: float) -> str:
        """
        描述趋势
        
        Args:
            pollution_trend: 污染指数趋势
            qualification_trend: 达标率趋势
            
        Returns:
            str: 趋势描述
        """
        if pollution_trend > 0.01:
            pollution_desc = "恶化"
        elif pollution_trend < -0.01:
            pollution_desc = "改善"
        else:
            pollution_desc = "稳定"
        
        if qualification_trend > 0.01:
            qualification_desc = "提高"
        elif qualification_trend < -0.01:
            qualification_desc = "下降"
        else:
            qualification_desc = "稳定"
        
        return f"污染指数呈{pollution_desc}趋势，达标率呈{qualification_desc}趋势"

    def _get_overall_assessment(self, qualification_rate: float, avg_pollution_index: float) -> str:
        """
        获取总体评价
        
        Args:
            qualification_rate: 达标率
            avg_pollution_index: 平均污染指数
            
        Returns:
            str: 总体评价
        """
        if qualification_rate >= 90 and avg_pollution_index <= 1.0:
            return "优秀"
        elif qualification_rate >= 80 and avg_pollution_index <= 1.5:
            return "良好"
        elif qualification_rate >= 60 and avg_pollution_index <= 2.0:
            return "一般"
        elif qualification_rate >= 40 and avg_pollution_index <= 3.0:
            return "较差"
        else:
            return "差"

    def generate_comprehensive_report(self, df: pd.DataFrame) -> Dict:
        """
        生成综合评价报告
        
        Args:
            df: 评价后的数据框
            
        Returns:
            Dict: 综合报告
        """
        # 总体统计
        total_samples = len(df)
        qualified_samples = df['is_qualified'].sum()
        overall_qualification_rate = qualified_samples / total_samples * 100
        
        # 水质类别分布
        overall_class_distribution = df['water_class'].value_counts().to_dict()
        overall_class_distribution_pct = (df['water_class'].value_counts(normalize=True) * 100).to_dict()
        
        # 站点分析
        station_analysis = self.analyze_station_quality(df)
        
        # 时间分析
        temporal_analysis = None
        if 'date' in df.columns:
            temporal_analysis = self._analyze_temporal_patterns(df)
        
        # 污染源分析
        pollution_source_analysis = self._analyze_pollution_sources(df)

        # 将污染源分析结果添加到站点分析中
        for station, psa_data in pollution_source_analysis.items():
            if station in station_analysis:
                station_analysis[station]['pollution_source_analysis'] = psa_data

        # 区域分析
        regional_analysis = self._analyze_regional_patterns(station_analysis)
        
        report = {
            'evaluation_summary': {
                'total_samples': total_samples,
                'qualified_samples': qualified_samples,
                'overall_qualification_rate': round(overall_qualification_rate, 2),
                'overall_class_distribution': overall_class_distribution,
                'overall_class_distribution_pct': {k: round(v, 2) for k, v in overall_class_distribution_pct.items()},
                'avg_pollution_index': round(df['pollution_index'].mean(), 3)
            },
            'station_analysis': station_analysis,
            'temporal_analysis': temporal_analysis,
            'pollution_source_analysis': pollution_source_analysis,
            'regional_analysis': regional_analysis,
            'recommendations': self._generate_recommendations(station_analysis, overall_qualification_rate)
        }
        
        return report

    def _analyze_temporal_patterns(self, df: pd.DataFrame) -> Dict:
        """
        分析时间模式
        
        Args:
            df: 数据框
            
        Returns:
            Dict: 时间分析结果
        """
        # 按年度分析
        yearly_analysis = df.groupby('year').agg({
            'pollution_index': 'mean',
            'is_qualified': 'mean',
            'water_class': lambda x: x.mode().iloc[0] if not x.empty else None
        }).round(3)
        
        # 按月份分析（季节性）
        monthly_analysis = df.groupby('month').agg({
            'pollution_index': 'mean',
            'is_qualified': 'mean'
        }).round(3)
        
        return {
            'yearly_analysis': yearly_analysis.to_dict(),
            'monthly_analysis': monthly_analysis.to_dict(),
            'seasonal_pattern': self._identify_seasonal_pattern(monthly_analysis)
        }

    def _identify_seasonal_pattern(self, monthly_analysis: pd.DataFrame) -> str:
        """
        识别季节性模式
        
        Args:
            monthly_analysis: 月度分析结果
            
        Returns:
            str: 季节性模式描述
        """
        # 简单的季节性分析
        spring_months = [3, 4, 5]
        summer_months = [6, 7, 8]
        autumn_months = [9, 10, 11]
        winter_months = [12, 1, 2]
        
        seasons = {
            '春季': monthly_analysis.loc[spring_months, 'pollution_index'].mean(),
            '夏季': monthly_analysis.loc[summer_months, 'pollution_index'].mean(),
            '秋季': monthly_analysis.loc[autumn_months, 'pollution_index'].mean(),
            '冬季': monthly_analysis.loc[winter_months, 'pollution_index'].mean()
        }
        
        worst_season = max(seasons, key=seasons.get)
        best_season = min(seasons, key=seasons.get)
        
        return f"水质污染程度：{worst_season}最严重，{best_season}最轻"

    def _analyze_pollution_sources(self, df: pd.DataFrame) -> Dict:
        """
        分析污染源特征
        
        Args:
            df: 数据框
            
        Returns:
            Dict: 污染源分析结果
        """
        # 计算各指标的超标情况
        exceedance_analysis = {}
        
        for station in df['station'].unique():
            station_data = df[df['station'] == station]
            
            # 计算各指标超标率
            ph_exceedance = ((station_data['ph'] < 6.0) | (station_data['ph'] > 9.0)).mean() * 100
            do_exceedance = (station_data['do'] < self.standards.do_standards[WaterQualityClass.CLASS_III]).mean() * 100
            codmn_exceedance = (station_data['codmn'] > self.standards.codmn_standards[WaterQualityClass.CLASS_III]).mean() * 100
            nh3n_exceedance = (station_data['nh3n'] > self.standards.nh3n_standards[WaterQualityClass.CLASS_III]).mean() * 100
            
            exceedance_analysis[station] = {
                'ph_exceedance_rate': round(ph_exceedance, 2),
                'do_exceedance_rate': round(do_exceedance, 2),
                'codmn_exceedance_rate': round(codmn_exceedance, 2),
                'nh3n_exceedance_rate': round(nh3n_exceedance, 2),
                'main_pollutant': self._identify_main_pollutant(
                    ph_exceedance, do_exceedance, codmn_exceedance, nh3n_exceedance
                )
            }
        
        return exceedance_analysis

    def _identify_main_pollutant(
        self, 
        ph_exc: float, 
        do_exc: float, 
        codmn_exc: float, 
        nh3n_exc: float
    ) -> str:
        """
        识别主要污染物
        
        Args:
            ph_exc: pH超标率
            do_exc: DO超标率
            codmn_exc: CODMn超标率
            nh3n_exc: NH3-N超标率
            
        Returns:
            str: 主要污染物
        """
        exceedances = {
            'pH': ph_exc,
            '溶解氧': do_exc,
            '高锰酸盐指数': codmn_exc,
            '氨氮': nh3n_exc
        }
        
        main_pollutant = max(exceedances, key=exceedances.get)
        max_rate = exceedances[main_pollutant]
        
        if max_rate < 10:
            return "无明显主要污染物"
        else:
            return main_pollutant

    def _analyze_regional_patterns(self, station_analysis: Dict) -> Dict:
        """
        分析区域模式

        Args:
            station_analysis: 站点分析结果

        Returns:
            Dict: 区域分析结果
        """
        # 按达标率排序
        stations_by_quality = sorted(
            station_analysis.items(),
            key=lambda x: x[1]['qualification_rate'],
            reverse=True
        )

        best_stations = stations_by_quality[:5]
        worst_stations = stations_by_quality[-5:]

        # 计算平均指标
        avg_qualification_rate = np.mean([data['qualification_rate'] for _, data in station_analysis.items()])
        avg_pollution_index = np.mean([data['avg_pollution_index'] for _, data in station_analysis.items()])

        # 区域分析（基于真实地理分布）
        regional_stats = self._analyze_by_region(station_analysis)

        # 污染源分析
        pollution_hotspots = self._identify_pollution_hotspots(station_analysis)

        return {
            'best_stations': [(station, data['qualification_rate']) for station, data in best_stations],
            'worst_stations': [(station, data['qualification_rate']) for station, data in worst_stations],
            'avg_qualification_rate': round(avg_qualification_rate, 2),
            'avg_pollution_index': round(avg_pollution_index, 3),
            'regional_summary': self._generate_regional_summary(station_analysis),
            'regional_stats': regional_stats,
            'pollution_hotspots': pollution_hotspots
        }

    def _analyze_by_region(self, station_analysis: Dict) -> Dict:
        """
        按区域分析水质状况

        Args:
            station_analysis: 站点分析结果

        Returns:
            Dict: 区域分析结果
        """
        # 定义区域分类
        region_mapping = {
            "四川攀枝花": "上游",
            "重庆朱沱": "上游",
            "四川乐山": "上游",
            "四川宜宾": "上游",
            "四川泸州": "上游",
            "湖北宜昌": "中游",
            "湖南岳阳": "中游",
            "江西九江": "中游",
            "湖北丹江口": "中游",
            "湖南长沙": "中游",
            "湖南岳阳楼": "中游",
            "湖北武汉": "中游",
            "江西南昌": "中游",
            "江西蛤蟆石": "中游",
            "安徽安庆": "下游",
            "江苏南京": "下游",
            "江苏扬州": "下游"
        }

        regional_data = {}

        for region in ["上游", "中游", "下游"]:
            region_stations = [station for station, mapped_region in region_mapping.items()
                             if mapped_region == region and station in station_analysis]

            if region_stations:
                qualification_rates = [station_analysis[station]['qualification_rate']
                                     for station in region_stations]
                pollution_indices = [station_analysis[station]['avg_pollution_index']
                                   for station in region_stations]

                regional_data[region] = {
                    'station_count': len(region_stations),
                    'avg_qualification_rate': round(np.mean(qualification_rates), 2),
                    'avg_pollution_index': round(np.mean(pollution_indices), 3),
                    'best_station': max(region_stations,
                                      key=lambda x: station_analysis[x]['qualification_rate']),
                    'worst_station': min(region_stations,
                                       key=lambda x: station_analysis[x]['qualification_rate']),
                    'stations': region_stations
                }

        return regional_data

    def _identify_pollution_hotspots(self, station_analysis: Dict) -> Dict:
        """
        识别污染热点区域

        Args:
            station_analysis: 站点分析结果

        Returns:
            Dict: 污染热点分析
        """
        # 基于真实数据识别的主要污染热点
        known_hotspots = {
            "江西南昌滁槎": {
                "pollution_type": "严重氨氮污染",
                "description": "赣江入鄱阳湖口，工业和生活污水排放严重",
                "main_pollutants": ["氨氮"],
                "severity": "极严重"
            },
            "四川泸州沱江": {
                "pollution_type": "综合污染",
                "description": "沱江入长江前，工业污染和生活污水影响",
                "main_pollutants": ["氨氮", "溶解氧"],
                "severity": "严重"
            },
            "湖南长沙新港": {
                "pollution_type": "氨氮污染",
                "description": "湘江洞庭湖入口，城市污水影响",
                "main_pollutants": ["氨氮"],
                "severity": "较严重"
            },
            "四川乐山岷江": {
                "pollution_type": "有机污染",
                "description": "岷江与大渡河汇合前，工业污染影响",
                "main_pollutants": ["高锰酸盐指数", "溶解氧"],
                "severity": "较严重"
            }
        }

        # 基于分析结果验证和补充
        identified_hotspots = {}

        for station, data in station_analysis.items():
            if data['qualification_rate'] < 50 or data['avg_pollution_index'] > 2.5:
                # 匹配已知热点或识别新热点
                matched_hotspot = None
                for hotspot_key in known_hotspots:
                    if any(keyword in station for keyword in hotspot_key.split()):
                        matched_hotspot = hotspot_key
                        break

                if matched_hotspot:
                    identified_hotspots[station] = known_hotspots[matched_hotspot].copy()
                    identified_hotspots[station]['qualification_rate'] = data['qualification_rate']
                    identified_hotspots[station]['pollution_index'] = data['avg_pollution_index']
                else:
                    identified_hotspots[station] = {
                        "pollution_type": "综合污染",
                        "description": "水质达标率低，需要重点关注",
                        "main_pollutants": ["待分析"],
                        "severity": "需关注",
                        'qualification_rate': data['qualification_rate'],
                        'pollution_index': data['avg_pollution_index']
                    }

        return identified_hotspots

    def _generate_regional_summary(self, station_analysis: Dict) -> str:
        """
        生成区域总结
        
        Args:
            station_analysis: 站点分析结果
            
        Returns:
            str: 区域总结
        """
        excellent_count = sum(1 for data in station_analysis.values() if data['overall_assessment'] == '优秀')
        good_count = sum(1 for data in station_analysis.values() if data['overall_assessment'] == '良好')
        poor_count = sum(1 for data in station_analysis.values() if data['overall_assessment'] in ['较差', '差'])
        
        total_stations = len(station_analysis)
        
        return f"共{total_stations}个监测站点，其中{excellent_count}个优秀，{good_count}个良好，{poor_count}个较差或差"

    def _generate_recommendations(self, station_analysis: Dict, overall_qualification_rate: float) -> List[str]:
        """
        生成建议
        
        Args:
            station_analysis: 站点分析结果
            overall_qualification_rate: 总体达标率
            
        Returns:
            List[str]: 建议列表
        """
        recommendations = []
        
        # 基于总体达标率的建议
        if overall_qualification_rate < 60:
            recommendations.append("总体水质达标率偏低，需要加强流域综合治理")
        elif overall_qualification_rate < 80:
            recommendations.append("水质状况有待改善，建议重点关注污染源控制")
        
        # 基于站点分析的建议
        poor_stations = [station for station, data in station_analysis.items() 
                        if data['overall_assessment'] in ['较差', '差']]
        
        if poor_stations:
            recommendations.append(f"重点关注{', '.join(poor_stations)}等站点的水质改善")
        
        # 基于主要污染物的建议
        main_pollutants = {}
        for station, data in station_analysis.items():
            if 'pollution_source_analysis' in data:
                pollutant = data['pollution_source_analysis'].get('main_pollutant', '无')
                if pollutant != '无明显主要污染物':
                    main_pollutants[pollutant] = main_pollutants.get(pollutant, 0) + 1
        
        if main_pollutants:
            top_pollutant = max(main_pollutants, key=main_pollutants.get)
            recommendations.append(f"重点控制{top_pollutant}污染")
        
        recommendations.append("建立长期监测机制，定期评估水质变化趋势")
        recommendations.append("加强上下游协调，实施流域一体化管理")
        
        return recommendations
