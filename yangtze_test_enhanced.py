#!/usr/bin/env python3
"""
长江水质综合评价分析系统 - 测试版
用于验证增强功能的简化版本
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from mpl_toolkits.mplot3d import Axes3D
import logging
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')


class YangtzeTestEnhancedAnalyzer:
    """长江水质分析器测试版"""
    
    def __init__(self):
        self.setup_logging()
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 水质类别评分
        self.class_scores = {
            'I': 100, 'II': 80, 'III': 60, 'IV': 40, 'V': 20, '劣V': 0
        }
        
        self.logger.info("测试版分析器初始化完成")
    
    def setup_logging(self):
        logging.basicConfig(level=logging.INFO, 
                          format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger('test_analyzer')
    
    def load_data(self, file_path='yangtze_complete_data_2003_2005.csv'):
        """加载数据"""
        try:
            df = pd.read_csv(file_path)
            df['date'] = pd.to_datetime(df['date'])
            df = df.dropna(subset=['ph', 'do', 'codmn', 'nh3n'])
            
            # 重新分类水质
            df['calculated_class'] = df.apply(self._classify_water_quality, axis=1)
            df['wqi'] = df['calculated_class'].map(self.class_scores)
            
            # 时间字段
            df['year_month'] = df['date'].dt.to_period('M')
            df['season'] = df['date'].dt.month.map({
                12: '冬季', 1: '冬季', 2: '冬季',
                3: '春季', 4: '春季', 5: '春季',
                6: '夏季', 7: '夏季', 8: '夏季',
                9: '秋季', 10: '秋季', 11: '秋季'
            })
            
            # 站点编码
            stations = df['station'].unique()
            station_codes = {station: i for i, station in enumerate(stations)}
            df['station_code'] = df['station'].map(station_codes)
            
            self.logger.info(f"数据加载完成：{len(df)}条记录，{df['station'].nunique()}个站点")
            return df
            
        except Exception as e:
            self.logger.error(f"数据加载失败：{e}")
            raise
    
    def _classify_water_quality(self, row):
        """水质分类"""
        ph, do, codmn, nh3n = row['ph'], row['do'], row['codmn'], row['nh3n']
        
        if ph < 6.0 or ph > 9.0:
            return '劣V'
        if do < 2.0 or codmn > 15.0 or nh3n > 2.0:
            return '劣V'
        elif do < 3.0 or codmn > 10.0 or nh3n > 1.5:
            return 'V'
        elif do < 5.0 or codmn > 6.0 or nh3n > 1.0:
            return 'IV'
        elif do < 6.0 or codmn > 4.0 or nh3n > 0.5:
            return 'III'
        elif do < 7.5 or codmn > 2.0 or nh3n > 0.15:
            return 'II'
        else:
            return 'I'
    
    def create_single_wqi_trend_chart(self, df):
        """创建单独的WQI趋势图"""
        fig, ax = plt.subplots(figsize=(14, 8))
        
        # 计算月度WQI
        monthly_wqi = df.groupby('year_month')['wqi'].mean()
        
        # 绘制趋势线
        ax.plot(range(len(monthly_wqi)), monthly_wqi.values, 
               marker='o', linewidth=3, markersize=6, color='#2E86AB', 
               label='月度WQI', alpha=0.8)
        
        # 添加趋势线
        if len(monthly_wqi) > 1:
            x_numeric = range(len(monthly_wqi))
            z = np.polyfit(x_numeric, monthly_wqi.values, 1)
            p = np.poly1d(z)
            trend_direction = '改善' if z[0] > 0 else '恶化' if z[0] < 0 else '稳定'
            ax.plot(x_numeric, p(x_numeric), 
                   "r--", alpha=0.8, linewidth=2, 
                   label=f'趋势线: {trend_direction}')
        
        # 添加参考线
        ax.axhline(y=80, color='green', linestyle=':', alpha=0.7, label='优秀水质线(80)')
        ax.axhline(y=60, color='orange', linestyle=':', alpha=0.7, label='良好水质线(60)')
        ax.axhline(y=40, color='red', linestyle=':', alpha=0.7, label='污染水质线(40)')
        
        # 填充区域
        ax.fill_between(range(len(monthly_wqi)), monthly_wqi.values, alpha=0.3, color='#2E86AB')
        
        ax.set_title('长江水质综合指数(WQI)时间变化趋势分析', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('时间序列（月份）', fontsize=12)
        ax.set_ylabel('WQI值', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=10)
        
        # 添加统计信息
        mean_wqi = monthly_wqi.mean()
        std_wqi = monthly_wqi.std()
        ax.text(0.02, 0.98, f'平均WQI: {mean_wqi:.1f}\n标准差: {std_wqi:.1f}', 
               transform=ax.transAxes, fontsize=10, verticalalignment='top',
               bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        filename = f'问题1_WQI时间趋势分析_{self.timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()  # 关闭图表释放内存
        self.logger.info(f"WQI时间趋势图已保存：{filename}")
        return filename
    
    def create_single_station_comparison_chart(self, df):
        """创建单独的站点对比图"""
        fig, ax = plt.subplots(figsize=(14, 10))
        
        station_wqi = df.groupby('station')['wqi'].mean().sort_values()
        colors = ['#d62728' if x < 40 else '#ff7f0e' if x < 60 else '#ffbb78' if x < 80 else '#2ca02c' 
                 for x in station_wqi.values]
        
        bars = ax.barh(range(len(station_wqi)), station_wqi.values, color=colors, alpha=0.8)
        
        # 设置y轴标签
        ax.set_yticks(range(len(station_wqi)))
        station_labels = [s[:12] + '...' if len(s) > 12 else s for s in station_wqi.index]
        ax.set_yticklabels(station_labels, fontsize=10)
        
        # 添加数值标签
        for i, (bar, value) in enumerate(zip(bars, station_wqi.values)):
            ax.text(value + 1, i, f'{value:.1f}', 
                   va='center', fontsize=9, fontweight='bold')
        
        # 添加参考线
        ax.axvline(x=80, color='green', linestyle='--', alpha=0.7, label='优秀水质线')
        ax.axvline(x=60, color='orange', linestyle='--', alpha=0.7, label='良好水质线')
        ax.axvline(x=40, color='red', linestyle='--', alpha=0.7, label='污染水质线')
        
        ax.set_title('长江各监测站点水质综合指数(WQI)对比分析', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('WQI值', fontsize=12)
        ax.set_ylabel('监测站点', fontsize=12)
        ax.grid(True, alpha=0.3, axis='x')
        ax.legend(fontsize=10)
        
        # 添加统计信息
        best_station = station_wqi.idxmax()
        worst_station = station_wqi.idxmin()
        ax.text(0.02, 0.98, f'最佳站点: {best_station[:15]}... ({station_wqi.max():.1f})\n最差站点: {worst_station[:15]}... ({station_wqi.min():.1f})', 
               transform=ax.transAxes, fontsize=10, verticalalignment='top',
               bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        
        plt.tight_layout()
        filename = f'问题1_各站点WQI对比分析_{self.timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()  # 关闭图表释放内存
        self.logger.info(f"各站点WQI对比图已保存：{filename}")
        return filename
    
    def create_single_3d_chart(self, df):
        """创建单独的3D图表"""
        fig = plt.figure(figsize=(12, 10))
        ax = fig.add_subplot(111, projection='3d')
        
        # 3D散点图：时间-空间-WQI
        x = df['station_code']  # 站点编码
        y = df['date'].dt.dayofyear  # 年内天数
        z = df['wqi']  # WQI值
        colors = df['wqi']  # 颜色映射
        
        scatter = ax.scatter(x, y, z, c=colors, cmap='RdYlGn', s=50, alpha=0.6)
        
        ax.set_xlabel('监测站点编码', fontsize=10)
        ax.set_ylabel('年内天数', fontsize=10)
        ax.set_zlabel('WQI值', fontsize=10)
        ax.set_title('长江水质时空分布三维散点图', fontsize=14, fontweight='bold')
        
        # 添加颜色条
        plt.colorbar(scatter, ax=ax, shrink=0.5, aspect=20, label='WQI值')
        
        plt.tight_layout()
        filename = f'问题1_三维时空分析_{self.timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()  # 关闭图表释放内存
        self.logger.info(f"三维时空分析图已保存：{filename}")
        return filename
    
    def run_test_analysis(self):
        """运行测试分析"""
        self.logger.info("开始测试分析...")
        
        # 加载数据
        df = self.load_data()
        
        # 生成独立图表
        generated_files = []
        
        try:
            # 1. WQI趋势图
            self.logger.info("生成WQI趋势图...")
            file1 = self.create_single_wqi_trend_chart(df)
            generated_files.append(file1)
            
            # 2. 站点对比图
            self.logger.info("生成站点对比图...")
            file2 = self.create_single_station_comparison_chart(df)
            generated_files.append(file2)
            
            # 3. 3D图表
            self.logger.info("生成3D图表...")
            file3 = self.create_single_3d_chart(df)
            generated_files.append(file3)
            
        except Exception as e:
            self.logger.error(f"图表生成失败：{e}")
            raise
        
        self.logger.info(f"测试分析完成！生成了 {len(generated_files)} 个图表文件")
        return generated_files


def main():
    """主函数"""
    try:
        analyzer = YangtzeTestEnhancedAnalyzer()
        generated_files = analyzer.run_test_analysis()
        
        print("\n" + "="*50)
        print("测试分析完成！")
        print("="*50)
        print("生成的图表文件：")
        for filename in generated_files:
            print(f"  - {filename}")
        print("="*50)
        
    except Exception as e:
        print(f"测试分析失败：{e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
