#!/usr/bin/env python3
"""
长江水质综合评价分析系统 - 问题(1)专用
简化版本，专注于核心功能

功能：
1. 对长江近两年多的水质情况做出定量的综合评价
2. 分析各地区水质的污染状况
3. 生成专业的可视化图表和分析报告

基于GB3838-2002《地表水环境质量标准》进行评价
数据来源：2003年6月-2005年9月长江水质监测数据

作者：长江水质分析系统
日期：2025-07-28
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class YangtzeWaterQualityProblem1Analyzer:
    """长江水质综合评价分析器 - 问题1专用"""
    
    def __init__(self):
        """初始化分析器"""
        self.setup_logging()
        
        # 水质类别评分
        self.class_scores = {
            'I': 100, 'II': 80, 'III': 60, 'IV': 40, 'V': 20, '劣V': 0
        }
        
        # 指标权重
        self.weights = {
            'do': 0.25,      # 溶解氧
            'codmn': 0.30,   # 高锰酸盐指数
            'nh3n': 0.30,    # 氨氮
            'ph': 0.15       # pH值
        }
        
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.logger.info("长江水质综合评价分析器初始化完成")
    
    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger('yangtze_problem1_analyzer')
    
    def load_and_process_data(self, file_path: str = 'yangtze_complete_data_2003_2005.csv') -> pd.DataFrame:
        """
        加载和处理水质数据
        
        Args:
            file_path: 数据文件路径
            
        Returns:
            pd.DataFrame: 处理后的水质数据
        """
        try:
            # 加载数据
            df = pd.read_csv(file_path)
            df['date'] = pd.to_datetime(df['date'])
            
            # 数据清洗
            df = df.dropna(subset=['ph', 'do', 'codmn', 'nh3n'])
            
            self.logger.info(f"成功加载水质数据：{len(df)}条记录")
            self.logger.info(f"时间范围：{df['date'].min()} 至 {df['date'].max()}")
            self.logger.info(f"监测站点：{df['station'].nunique()}个")
            
            # 重新分类水质
            df['calculated_class'] = df.apply(self._classify_water_quality, axis=1)
            
            # 计算WQI
            df['wqi'] = df['calculated_class'].map(self.class_scores)
            
            # 计算污染指数
            df = self._calculate_pollution_indices(df)
            
            # 污染程度分级
            df['pollution_level'] = df['wqi'].apply(self._get_pollution_level)
            
            # 时间相关字段
            df['year_month'] = df['date'].dt.to_period('M')
            df['season'] = df['date'].dt.month.map({
                12: '冬季', 1: '冬季', 2: '冬季',
                3: '春季', 4: '春季', 5: '春季',
                6: '夏季', 7: '夏季', 8: '夏季',
                9: '秋季', 10: '秋季', 11: '秋季'
            })
            df['year'] = df['date'].dt.year
            
            self.logger.info("数据处理完成")
            return df
            
        except Exception as e:
            self.logger.error(f"数据加载失败：{e}")
            raise
    
    def _classify_water_quality(self, row: pd.Series) -> str:
        """
        根据GB3838-2002标准分类水质
        
        Args:
            row: 数据行
            
        Returns:
            str: 水质类别
        """
        ph, do, codmn, nh3n = row['ph'], row['do'], row['codmn'], row['nh3n']
        
        # pH值检查
        if ph < 6.0 or ph > 9.0:
            return '劣V'
        
        # 按最差指标确定类别
        if do < 2.0 or codmn > 15.0 or nh3n > 2.0:
            return '劣V'
        elif do < 3.0 or codmn > 10.0 or nh3n > 1.5:
            return 'V'
        elif do < 5.0 or codmn > 6.0 or nh3n > 1.0:
            return 'IV'
        elif do < 6.0 or codmn > 4.0 or nh3n > 0.5:
            return 'III'
        elif do < 7.5 or codmn > 2.0 or nh3n > 0.15:
            return 'II'
        else:
            return 'I'
    
    def _calculate_pollution_indices(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算污染指数"""
        df = df.copy()
        
        # 以III类标准为基准计算污染指数
        df['do_pi'] = 5.0 / df['do']  # DO污染指数
        df['codmn_pi'] = df['codmn'] / 6.0  # CODMn污染指数
        df['nh3n_pi'] = df['nh3n'] / 1.0  # NH3-N污染指数
        
        # pH污染指数
        df['ph_pi'] = np.where(
            (df['ph'] >= 6.0) & (df['ph'] <= 9.0), 0,
            np.maximum(np.abs(df['ph'] - 6.0), np.abs(df['ph'] - 9.0))
        )
        
        # 综合污染指数
        df['comprehensive_pi'] = (
            df['do_pi'] * self.weights['do'] +
            df['codmn_pi'] * self.weights['codmn'] +
            df['nh3n_pi'] * self.weights['nh3n'] +
            df['ph_pi'] * self.weights['ph']
        )
        
        return df
    
    def _get_pollution_level(self, wqi: float) -> str:
        """获取污染程度等级"""
        if wqi >= 80:
            return '优秀'
        elif wqi >= 60:
            return '良好'
        elif wqi >= 40:
            return '轻度污染'
        elif wqi >= 20:
            return '中度污染'
        else:
            return '重度污染'
    
    def analyze_spatial_distribution(self, df: pd.DataFrame) -> Dict:
        """空间分布分析"""
        spatial_results = {}
        
        # 各站点基本统计
        station_stats = df.groupby('station').agg({
            'wqi': ['mean', 'std', 'min', 'max'],
            'comprehensive_pi': 'mean',
            'calculated_class': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else 'III'
        }).round(2)
        
        # 污染程度统计
        pollution_counts = df.groupby('station')['pollution_level'].value_counts().unstack(fill_value=0)
        pollution_rates = pollution_counts.div(pollution_counts.sum(axis=1), axis=0) * 100
        
        # 水质类别分布
        class_distribution = df.groupby('station')['calculated_class'].value_counts().unstack(fill_value=0)
        class_percentage = class_distribution.div(class_distribution.sum(axis=1), axis=0) * 100
        
        # 主要污染物识别
        pollutant_stats = df.groupby('station').agg({
            'do_pi': 'mean',
            'codmn_pi': 'mean', 
            'nh3n_pi': 'mean',
            'ph_pi': 'mean'
        }).round(3)
        
        spatial_results = {
            'station_stats': station_stats,
            'pollution_rates': pollution_rates,
            'class_distribution': class_distribution,
            'class_percentage': class_percentage,
            'pollutant_stats': pollutant_stats
        }
        
        self.logger.info("空间分析完成")
        return spatial_results
    
    def analyze_temporal_trends(self, df: pd.DataFrame) -> Dict:
        """时间趋势分析"""
        temporal_results = {}
        
        # 月度统计
        monthly_wqi = df.groupby('year_month')['wqi'].mean()
        monthly_stats = df.groupby('year_month').agg({
            'wqi': ['mean', 'std'],
            'comprehensive_pi': 'mean'
        }).round(2)
        
        # 季节性分析
        seasonal_stats = df.groupby('season').agg({
            'wqi': ['mean', 'std'],
            'comprehensive_pi': 'mean',
            'do': 'mean',
            'codmn': 'mean',
            'nh3n': 'mean'
        }).round(2)
        
        # 年度趋势
        yearly_stats = df.groupby('year').agg({
            'wqi': ['mean', 'std'],
            'comprehensive_pi': 'mean'
        }).round(2)
        
        # 趋势分析
        if len(monthly_wqi) > 1:
            trend_slope = np.polyfit(range(len(monthly_wqi)), monthly_wqi.values, 1)[0]
            trend_direction = '改善' if trend_slope > 0 else '恶化' if trend_slope < 0 else '稳定'
        else:
            trend_slope = 0
            trend_direction = '稳定'
        
        temporal_results = {
            'monthly_wqi': monthly_wqi,
            'monthly_stats': monthly_stats,
            'seasonal_stats': seasonal_stats,
            'yearly_stats': yearly_stats,
            'trend_slope': trend_slope,
            'trend_direction': trend_direction
        }
        
        self.logger.info("时间分析完成")
        return temporal_results

    def create_comprehensive_visualizations(self, df: pd.DataFrame, spatial_results: Dict, temporal_results: Dict):
        """创建综合可视化图表"""

        # 图表1：综合评价概览
        fig1, axes1 = plt.subplots(2, 2, figsize=(16, 12))
        fig1.suptitle('长江水质综合评价分析 - 问题(1)', fontsize=16, fontweight='bold')

        # 1.1 WQI时间趋势
        monthly_wqi = temporal_results['monthly_wqi']
        axes1[0, 0].plot(range(len(monthly_wqi)), monthly_wqi.values,
                        marker='o', linewidth=2, markersize=4, color='blue')
        axes1[0, 0].set_title('水质综合指数(WQI)时间变化趋势', fontsize=12, fontweight='bold')
        axes1[0, 0].set_xlabel('时间序列')
        axes1[0, 0].set_ylabel('WQI值')
        axes1[0, 0].grid(True, alpha=0.3)

        # 添加趋势线
        if len(monthly_wqi) > 1:
            x_numeric = range(len(monthly_wqi))
            z = np.polyfit(x_numeric, monthly_wqi.values, 1)
            p = np.poly1d(z)
            axes1[0, 0].plot(x_numeric, p(x_numeric),
                           "r--", alpha=0.8, linewidth=2,
                           label=f'趋势: {temporal_results["trend_direction"]}')
            axes1[0, 0].legend()

        # 1.2 各站点WQI对比
        station_wqi = spatial_results['station_stats']['wqi']['mean'].sort_values()
        colors = ['red' if x < 40 else 'orange' if x < 60 else 'yellow' if x < 80 else 'green'
                 for x in station_wqi.values]

        bars = axes1[0, 1].barh(range(len(station_wqi)), station_wqi.values, color=colors, alpha=0.7)
        axes1[0, 1].set_yticks(range(len(station_wqi)))
        axes1[0, 1].set_yticklabels([s[:8] + '...' if len(s) > 8 else s for s in station_wqi.index], fontsize=8)
        axes1[0, 1].set_title('各地区水质综合指数(WQI)对比', fontsize=12, fontweight='bold')
        axes1[0, 1].set_xlabel('WQI值')
        axes1[0, 1].grid(True, alpha=0.3, axis='x')

        # 添加数值标签
        for i, (bar, value) in enumerate(zip(bars, station_wqi.values)):
            axes1[0, 1].text(value + 1, i, f'{value:.1f}',
                           va='center', fontsize=8)

        # 1.3 水质类别分布
        class_counts = df['calculated_class'].value_counts()
        colors_pie = ['green', 'lightgreen', 'yellow', 'orange', 'red', 'darkred']
        axes1[1, 0].pie(class_counts.values,
                        labels=class_counts.index,
                        colors=colors_pie[:len(class_counts)],
                        autopct='%1.1f%%',
                        startangle=90)
        axes1[1, 0].set_title('水质类别分布', fontsize=12, fontweight='bold')

        # 1.4 季节性变化
        seasonal_wqi = temporal_results['seasonal_stats']['wqi']['mean']
        colors_season = ['lightblue', 'lightgreen', 'orange', 'brown']

        bars = axes1[1, 1].bar(seasonal_wqi.index, seasonal_wqi.values,
                              color=colors_season, alpha=0.7)
        axes1[1, 1].set_title('季节性水质变化', fontsize=12, fontweight='bold')
        axes1[1, 1].set_ylabel('平均WQI值')
        axes1[1, 1].grid(True, alpha=0.3, axis='y')

        # 添加数值标签
        for bar, value in zip(bars, seasonal_wqi.values):
            axes1[1, 1].text(bar.get_x() + bar.get_width()/2, value + 1,
                           f'{value:.1f}', ha='center', va='bottom', fontsize=10)

        plt.tight_layout()

        # 保存图表1
        filename1 = f'问题1_水质综合评价_{self.timestamp}.png'
        plt.savefig(filename1, dpi=300, bbox_inches='tight')
        self.logger.info(f"综合评价图表已保存：{filename1}")
        plt.show()

        # 图表2：污染状况详细分析
        fig2, axes2 = plt.subplots(2, 2, figsize=(16, 12))
        fig2.suptitle('长江各地区污染状况详细分析 - 问题(1)', fontsize=16, fontweight='bold')

        # 2.1 重度污染比例
        if 'severe_rate' in spatial_results['pollution_rates'].columns:
            severe_rates = spatial_results['pollution_rates']['severe_rate'].fillna(0)
        else:
            severe_rates = pd.Series(0, index=spatial_results['pollution_rates'].index)

        colors = ['darkred' if x > 20 else 'red' if x > 10 else 'orange' if x > 5 else 'green'
                 for x in severe_rates.values]

        bars = axes2[0, 0].barh(range(len(severe_rates)), severe_rates.values, color=colors, alpha=0.7)
        axes2[0, 0].set_yticks(range(len(severe_rates)))
        axes2[0, 0].set_yticklabels([s[:8] + '...' if len(s) > 8 else s for s in severe_rates.index], fontsize=8)
        axes2[0, 0].set_title('各地区重度污染比例', fontsize=12, fontweight='bold')
        axes2[0, 0].set_xlabel('重度污染比例 (%)')
        axes2[0, 0].grid(True, alpha=0.3, axis='x')

        # 2.2 主要污染物对比
        pollutant_means = df[['do_pi', 'codmn_pi', 'nh3n_pi']].mean()
        pollutant_names = ['溶解氧', '高锰酸盐指数', '氨氮']
        colors_bar = ['skyblue', 'lightcoral', 'lightgreen']

        bars = axes2[0, 1].bar(pollutant_names, pollutant_means.values,
                             color=colors_bar, alpha=0.7)
        axes2[0, 1].set_title('主要污染物污染指数对比', fontsize=12, fontweight='bold')
        axes2[0, 1].set_ylabel('污染指数')
        axes2[0, 1].axhline(y=1.0, color='red', linestyle='--', alpha=0.7, label='标准线(1.0)')
        axes2[0, 1].legend()
        axes2[0, 1].grid(True, alpha=0.3, axis='y')

        # 添加数值标签
        for bar, value in zip(bars, pollutant_means.values):
            axes2[0, 1].text(bar.get_x() + bar.get_width()/2, value + 0.02,
                           f'{value:.2f}', ha='center', va='bottom', fontsize=10)

        # 2.3 污染物浓度分布
        pollutants_data = df[['codmn', 'nh3n']].melt(var_name='pollutant', value_name='concentration')
        pollutants_data['pollutant'] = pollutants_data['pollutant'].map({
            'codmn': '高锰酸盐指数', 'nh3n': '氨氮'
        })

        sns.boxplot(data=pollutants_data, x='pollutant', y='concentration', ax=axes2[1, 0])
        axes2[1, 0].set_title('主要污染物浓度分布', fontsize=12, fontweight='bold')
        axes2[1, 0].set_xlabel('污染物')
        axes2[1, 0].set_ylabel('浓度 (mg/L)')
        axes2[1, 0].grid(True, alpha=0.3, axis='y')

        # 2.4 污染程度分布
        pollution_dist = df['pollution_level'].value_counts()
        colors_pollution = ['green', 'lightgreen', 'yellow', 'orange', 'red']

        axes2[1, 1].pie(pollution_dist.values, labels=pollution_dist.index,
                       colors=colors_pollution[:len(pollution_dist)], autopct='%1.1f%%', startangle=90)
        axes2[1, 1].set_title('污染程度分布', fontsize=12, fontweight='bold')

        plt.tight_layout()

        # 保存图表2
        filename2 = f'问题1_污染状况分析_{self.timestamp}.png'
        plt.savefig(filename2, dpi=300, bbox_inches='tight')
        self.logger.info(f"污染状况分析图表已保存：{filename2}")
        plt.show()

    def generate_comprehensive_report(self, df: pd.DataFrame, spatial_results: Dict, temporal_results: Dict) -> str:
        """生成综合评价报告"""
        report = []
        report.append("=" * 80)
        report.append("长江水质综合评价分析报告 - 问题(1)")
        report.append("=" * 80)
        report.append(f"分析时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}")
        report.append(f"数据来源：长江水质监测数据（2003年6月-2005年9月）")
        report.append(f"评价标准：GB3838-2002《地表水环境质量标准》")
        report.append("")

        # 1. 数据概况
        report.append("一、数据概况")
        report.append("-" * 40)
        report.append(f"• 监测记录总数：{len(df):,}条")
        report.append(f"• 监测站点数量：{df['station'].nunique()}个")
        report.append(f"• 监测时间跨度：{df['date'].min().strftime('%Y年%m月')} - {df['date'].max().strftime('%Y年%m月')}")
        report.append(f"• 监测指标：pH、溶解氧(DO)、高锰酸盐指数(CODMn)、氨氮(NH3-N)")
        report.append("")

        # 2. 定量综合评价
        report.append("二、定量综合评价结果")
        report.append("-" * 40)

        overall_wqi = df['wqi'].mean()
        overall_level = df['pollution_level'].mode().iloc[0] if len(df['pollution_level'].mode()) > 0 else '中度污染'

        report.append(f"• 长江整体水质综合指数(WQI)：{overall_wqi:.1f}")
        report.append(f"• 整体污染程度：{overall_level}")

        # 水质类别分布
        class_dist = df['calculated_class'].value_counts(normalize=True) * 100
        report.append(f"• 水质类别分布：")
        for cls in ['I', 'II', 'III', 'IV', 'V', '劣V']:
            if cls in class_dist.index:
                report.append(f"  - {cls}类水：{class_dist[cls]:.1f}%")

        # 可饮用水比例
        drinkable_classes = ['I', 'II', 'III']
        drinkable_rate = class_dist[class_dist.index.isin(drinkable_classes)].sum()
        report.append(f"• 可饮用水比例（I-III类）：{drinkable_rate:.1f}%")

        # 污染水比例
        polluted_classes = ['IV', 'V', '劣V']
        polluted_rate = class_dist[class_dist.index.isin(polluted_classes)].sum()
        report.append(f"• 污染水比例（IV类及以下）：{polluted_rate:.1f}%")
        report.append("")

        # 3. 各地区污染状况分析
        report.append("三、各地区污染状况分析")
        report.append("-" * 40)

        station_wqi = spatial_results['station_stats']['wqi']['mean'].sort_values(ascending=False)

        report.append("• 各地区水质综合指数(WQI)排名：")
        for i, (station, wqi) in enumerate(station_wqi.items(), 1):
            level = '优秀' if wqi >= 80 else '良好' if wqi >= 60 else '轻度污染' if wqi >= 40 else '中度污染' if wqi >= 20 else '重度污染'
            report.append(f"  {i:2d}. {station:<15} WQI={wqi:5.1f} ({level})")

        report.append("")

        # 4. 时间变化趋势
        report.append("四、时间变化趋势分析")
        report.append("-" * 40)

        trend_direction = temporal_results['trend_direction']
        trend_slope = temporal_results['trend_slope']

        report.append(f"• 整体趋势：水质呈{trend_direction}趋势")
        report.append(f"• 趋势斜率：{trend_slope:.3f} (WQI单位/月)")

        # 季节性特征
        seasonal_wqi = temporal_results['seasonal_stats']['wqi']['mean']
        best_season = seasonal_wqi.idxmax()
        worst_season = seasonal_wqi.idxmin()

        report.append(f"• 季节性特征：")
        report.append(f"  - 水质最好季节：{best_season}（WQI={seasonal_wqi[best_season]:.1f}）")
        report.append(f"  - 水质最差季节：{worst_season}（WQI={seasonal_wqi[worst_season]:.1f}）")

        report.append("")

        # 5. 主要结论
        report.append("五、主要结论与建议")
        report.append("-" * 40)

        if overall_wqi >= 60:
            conclusion = "长江整体水质状况良好"
        elif overall_wqi >= 40:
            conclusion = "长江整体水质存在轻度污染"
        elif overall_wqi >= 20:
            conclusion = "长江整体水质存在中度污染，需要重点治理"
        else:
            conclusion = "长江整体水质存在重度污染，情况严峻"

        report.append(f"• 总体评价：{conclusion}")

        if polluted_rate > 50:
            report.append(f"• 重点问题：污染水比例高达{polluted_rate:.1f}%，超过一半监测点水质不达标")

        # 主要污染源
        pollutant_means = df[['do_pi', 'codmn_pi', 'nh3n_pi']].mean()
        main_pollutant_idx = pollutant_means.idxmax()
        main_pollutant_name = {'do_pi': '溶解氧', 'codmn_pi': '高锰酸盐指数', 'nh3n_pi': '氨氮'}[main_pollutant_idx]
        report.append(f"• 主要污染源：{main_pollutant_name}是当前最主要的污染因子")

        report.append("")
        report.append("=" * 80)
        report.append(f"报告生成时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}")
        report.append("=" * 80)

        return "\n".join(report)

    def run_analysis(self) -> Dict:
        """运行完整分析"""
        self.logger.info("开始长江水质综合评价分析...")

        # 1. 加载和处理数据
        df = self.load_and_process_data()

        # 2. 空间分析
        spatial_results = self.analyze_spatial_distribution(df)

        # 3. 时间分析
        temporal_results = self.analyze_temporal_trends(df)

        # 4. 生成可视化
        self.create_comprehensive_visualizations(df, spatial_results, temporal_results)

        # 5. 生成报告
        report = self.generate_comprehensive_report(df, spatial_results, temporal_results)

        # 保存报告
        report_filename = f'问题1_长江水质综合评价报告_{self.timestamp}.txt'
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report)

        self.logger.info(f"综合评价报告已保存：{report_filename}")

        # 保存分析数据
        analysis_data = df[['date', 'station', 'wqi', 'pollution_level', 'comprehensive_pi',
                           'calculated_class', 'do_pi', 'codmn_pi', 'nh3n_pi']].copy()
        data_filename = f'问题1_评价结果数据_{self.timestamp}.csv'
        analysis_data.to_csv(data_filename, index=False, encoding='utf-8-sig')

        self.logger.info(f"评价结果数据已保存：{data_filename}")

        results = {
            'dataframe': df,
            'spatial_results': spatial_results,
            'temporal_results': temporal_results,
            'report': report,
            'overall_wqi': df['wqi'].mean(),
            'pollution_rate': (df['calculated_class'].isin(['IV', 'V', '劣V'])).mean() * 100
        }

        self.logger.info("长江水质综合评价分析完成！")
        return results


if __name__ == "__main__":
    # 运行分析
    analyzer = YangtzeWaterQualityProblem1Analyzer()
    results = analyzer.run_analysis()

    print("\n" + "=" * 80)
    print("长江水质综合评价分析 - 问题(1) 完成")
    print("=" * 80)
    print(f"整体水质综合指数(WQI)：{results['overall_wqi']:.1f}")
    print(f"污染水比例：{results['pollution_rate']:.1f}%")
    print("详细分析结果请查看生成的图表和报告文件。")
    print("=" * 80)
