#!/usr/bin/env python3
"""
长江水质综合评价分析系统 - 问题1最终图表生成器
专门生成符合用户要求的"问题1+时间戳"格式的图表
基于已验证工作的代码，确保稳定性
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
import logging
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def setup_logging():
    logging.basicConfig(level=logging.INFO, 
                      format='%(asctime)s - %(levelname)s - %(message)s')
    return logging.getLogger('problem1_final_charts')

def classify_water_quality(row):
    """根据GB3838-2002标准分类水质"""
    ph, do, codmn, nh3n = row['ph'], row['do'], row['codmn'], row['nh3n']
    
    if ph < 6.0 or ph > 9.0:
        return '劣V'
    if do < 2.0 or codmn > 15.0 or nh3n > 2.0:
        return '劣V'
    elif do < 3.0 or codmn > 10.0 or nh3n > 1.5:
        return 'V'
    elif do < 5.0 or codmn > 6.0 or nh3n > 1.0:
        return 'IV'
    elif do < 6.0 or codmn > 4.0 or nh3n > 0.5:
        return 'III'
    elif do < 7.5 or codmn > 2.0 or nh3n > 0.15:
        return 'II'
    else:
        return 'I'

def calculate_enhanced_indices(df):
    """计算增强评价指标体系"""
    df = df.copy()
    
    # 基础WQI计算
    class_scores = {'I': 100, 'II': 80, 'III': 60, 'IV': 40, 'V': 20, '劣V': 0}
    df['wqi'] = df['calculated_class'].map(class_scores)
    
    # 污染指数计算
    df['do_pi'] = 5.0 / df['do']
    df['codmn_pi'] = df['codmn'] / 6.0
    df['nh3n_pi'] = df['nh3n'] / 1.0
    df['ph_pi'] = np.where(
        (df['ph'] >= 6.0) & (df['ph'] <= 9.0), 0,
        np.maximum(np.abs(df['ph'] - 6.0), np.abs(df['ph'] - 9.0))
    )
    
    # 综合污染指数
    weights = {'do': 0.25, 'codmn': 0.30, 'nh3n': 0.30, 'ph': 0.15}
    df['comprehensive_pi'] = (
        df['do_pi'] * weights['do'] +
        df['codmn_pi'] * weights['codmn'] +
        df['nh3n_pi'] * weights['nh3n'] +
        df['ph_pi'] * weights['ph']
    )
    
    # 生态风险评估
    df['ecological_risk'] = 0
    for idx, row in df.iterrows():
        risk = 0
        if row['do'] < 3.0: risk += 3
        elif row['do'] < 5.0: risk += 2
        elif row['do'] < 6.0: risk += 1
        
        if row['codmn'] > 10.0: risk += 3
        elif row['codmn'] > 6.0: risk += 2
        elif row['codmn'] > 4.0: risk += 1
        
        if row['nh3n'] > 1.5: risk += 3
        elif row['nh3n'] > 1.0: risk += 2
        elif row['nh3n'] > 0.5: risk += 1
        
        df.at[idx, 'ecological_risk'] = risk
    
    # 富营养化指数
    df['eutrophication_index'] = 0
    for idx, row in df.iterrows():
        score = 0
        if row['nh3n'] > 2.0: score += 4
        elif row['nh3n'] > 1.0: score += 3
        elif row['nh3n'] > 0.5: score += 2
        elif row['nh3n'] > 0.15: score += 1
        
        if row['codmn'] > 10.0: score += 3
        elif row['codmn'] > 6.0: score += 2
        elif row['codmn'] > 4.0: score += 1
        
        df.at[idx, 'eutrophication_index'] = score
    
    return df

def create_chart_1_wqi_trend(df, timestamp):
    """图表1：WQI时间趋势分析"""
    plt.figure(figsize=(14, 8))
    
    monthly_wqi = df.groupby('year_month_str')['wqi'].mean()
    
    plt.plot(range(len(monthly_wqi)), monthly_wqi.values, 
           marker='o', linewidth=3, markersize=6, color='#2E86AB', 
           label='月度WQI', alpha=0.8)
    
    # 添加趋势线
    if len(monthly_wqi) > 1:
        x_numeric = range(len(monthly_wqi))
        z = np.polyfit(x_numeric, monthly_wqi.values, 1)
        p = np.poly1d(z)
        trend_direction = '改善' if z[0] > 0 else '恶化' if z[0] < 0 else '稳定'
        plt.plot(x_numeric, p(x_numeric), 
               "r--", alpha=0.8, linewidth=2, 
               label=f'趋势线: {trend_direction}')
    
    # 添加水质等级参考线
    plt.axhline(y=80, color='green', linestyle=':', alpha=0.7, label='优秀水质线(80)')
    plt.axhline(y=60, color='orange', linestyle=':', alpha=0.7, label='良好水质线(60)')
    plt.axhline(y=40, color='red', linestyle=':', alpha=0.7, label='污染水质线(40)')
    
    plt.fill_between(range(len(monthly_wqi)), monthly_wqi.values, alpha=0.3, color='#2E86AB')
    
    plt.title('长江水质综合指数(WQI)时间变化趋势分析', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('时间序列（月份）', fontsize=12)
    plt.ylabel('WQI值', fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.legend(fontsize=10)
    
    # 添加统计信息
    mean_wqi = monthly_wqi.mean()
    std_wqi = monthly_wqi.std()
    plt.text(0.02, 0.98, f'平均WQI: {mean_wqi:.1f}\n标准差: {std_wqi:.1f}', 
           transform=plt.gca().transAxes, fontsize=10, verticalalignment='top',
           bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    filename = f'问题1_WQI时间趋势分析_{timestamp}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()
    return filename

def create_chart_2_station_comparison(df, timestamp):
    """图表2：各站点WQI对比分析"""
    plt.figure(figsize=(14, 10))
    
    station_wqi = df.groupby('station')['wqi'].mean().sort_values()
    colors = ['#d62728' if x < 40 else '#ff7f0e' if x < 60 else '#ffbb78' if x < 80 else '#2ca02c' 
             for x in station_wqi.values]
    
    plt.barh(range(len(station_wqi)), station_wqi.values, color=colors, alpha=0.8)
    
    plt.yticks(range(len(station_wqi)), 
              [s[:12] + '...' if len(s) > 12 else s for s in station_wqi.index], 
              fontsize=10)
    
    # 添加数值标签
    for i, value in enumerate(station_wqi.values):
        plt.text(value + 1, i, f'{value:.1f}', 
               va='center', fontsize=9, fontweight='bold')
    
    # 添加参考线
    plt.axvline(x=80, color='green', linestyle='--', alpha=0.7, label='优秀水质线')
    plt.axvline(x=60, color='orange', linestyle='--', alpha=0.7, label='良好水质线')
    plt.axvline(x=40, color='red', linestyle='--', alpha=0.7, label='污染水质线')
    
    plt.title('长江各监测站点水质综合指数(WQI)对比分析', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('WQI值', fontsize=12)
    plt.ylabel('监测站点', fontsize=12)
    plt.grid(True, alpha=0.3, axis='x')
    plt.legend(fontsize=10)
    
    # 添加统计信息
    best_station = station_wqi.idxmax()
    worst_station = station_wqi.idxmin()
    plt.text(0.02, 0.98, f'最佳站点: {best_station[:15]}... ({station_wqi.max():.1f})\n最差站点: {worst_station[:15]}... ({station_wqi.min():.1f})', 
           transform=plt.gca().transAxes, fontsize=10, verticalalignment='top',
           bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    plt.tight_layout()
    filename = f'问题1_各站点WQI对比分析_{timestamp}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()
    return filename

def create_chart_3_water_class_distribution(df, timestamp):
    """图表3：水质类别分布分析"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 左图：饼图
    class_counts = df['calculated_class'].value_counts()
    colors_pie = ['#2ca02c', '#98df8a', '#ffbb78', '#ff7f0e', '#d62728', '#8c564b']
    
    ax1.pie(class_counts.values, 
           labels=class_counts.index,
           colors=colors_pie[:len(class_counts)],
           autopct='%1.1f%%',
           startangle=90,
           explode=[0.05 if cls in ['劣V', 'V'] else 0 for cls in class_counts.index])
    
    ax1.set_title('水质类别分布比例', fontsize=14, fontweight='bold')
    
    # 右图：柱状图
    bars = ax2.bar(class_counts.index, class_counts.values, 
                  color=colors_pie[:len(class_counts)], alpha=0.8)
    
    # 添加数值标签
    for bar, value in zip(bars, class_counts.values):
        ax2.text(bar.get_x() + bar.get_width()/2, value + 2, 
                f'{value}\n({value/len(df)*100:.1f}%)', 
                ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    ax2.set_title('水质类别记录数量统计', fontsize=14, fontweight='bold')
    ax2.set_xlabel('水质类别', fontsize=12)
    ax2.set_ylabel('记录数量', fontsize=12)
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 添加总体评价
    drinkable_rate = class_counts[class_counts.index.isin(['I', 'II', 'III'])].sum() / len(df) * 100
    polluted_rate = class_counts[class_counts.index.isin(['IV', 'V', '劣V'])].sum() / len(df) * 100
    
    fig.suptitle(f'长江水质类别分布分析\n可饮用水比例: {drinkable_rate:.1f}% | 污染水比例: {polluted_rate:.1f}%', 
                fontsize=16, fontweight='bold')
    
    plt.tight_layout()
    filename = f'问题1_水质类别分布分析_{timestamp}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()
    return filename

def main():
    """主函数 - 生成问题1的正式图表"""
    logger = setup_logging()
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    try:
        logger.info("开始生成问题1正式图表...")

        # 1. 数据加载和处理
        logger.info("加载和处理数据...")
        df = pd.read_csv('yangtze_complete_data_2003_2005.csv')
        df['date'] = pd.to_datetime(df['date'])
        df = df.dropna(subset=['ph', 'do', 'codmn', 'nh3n'])

        # 水质分类
        df['calculated_class'] = df.apply(classify_water_quality, axis=1)

        # 计算增强评价指标
        df = calculate_enhanced_indices(df)

        # 时间字段
        df['year_month_str'] = df['date'].dt.strftime('%Y-%m')
        df['season'] = df['date'].dt.month.map({
            12: '冬季', 1: '冬季', 2: '冬季',
            3: '春季', 4: '春季', 5: '春季',
            6: '夏季', 7: '夏季', 8: '夏季',
            9: '秋季', 10: '秋季', 11: '秋季'
        })

        logger.info(f"数据处理完成：{len(df)}条记录，{df['station'].nunique()}个站点")

        # 2. 生成正式图表
        generated_files = {}

        logger.info("生成图表1：WQI时间趋势分析...")
        generated_files['wqi_trend'] = create_chart_1_wqi_trend(df, timestamp)
        logger.info(f"图表1生成成功：{generated_files['wqi_trend']}")

        logger.info("生成图表2：各站点WQI对比分析...")
        generated_files['station_comparison'] = create_chart_2_station_comparison(df, timestamp)
        logger.info(f"图表2生成成功：{generated_files['station_comparison']}")

        logger.info("生成图表3：水质类别分布分析...")
        generated_files['water_class_distribution'] = create_chart_3_water_class_distribution(df, timestamp)
        logger.info(f"图表3生成成功：{generated_files['water_class_distribution']}")

        # 3. 生成简要报告
        report_filename = f'问题1_正式分析报告_{timestamp}.txt'

        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("长江水质综合评价分析报告（问题1正式版）\n")
            f.write("=" * 80 + "\n\n")

            # 数据概况
            f.write("1. 数据概况\n")
            f.write("-" * 40 + "\n")
            f.write(f"分析时间范围：{df['date'].min()} 至 {df['date'].max()}\n")
            f.write(f"监测站点数量：{df['station'].nunique()} 个\n")
            f.write(f"监测记录总数：{len(df)} 条\n\n")

            # 水质总体评价
            f.write("2. 水质总体评价\n")
            f.write("-" * 40 + "\n")
            avg_wqi = df['wqi'].mean()
            f.write(f"平均水质综合指数(WQI)：{avg_wqi:.1f}\n")

            if avg_wqi >= 80:
                overall_level = '优秀'
            elif avg_wqi >= 60:
                overall_level = '良好'
            elif avg_wqi >= 40:
                overall_level = '轻度污染'
            elif avg_wqi >= 20:
                overall_level = '中度污染'
            else:
                overall_level = '重度污染'

            f.write(f"总体水质等级：{overall_level}\n\n")

            # 水质类别分布
            f.write("3. 水质类别分布\n")
            f.write("-" * 40 + "\n")
            class_counts = df['calculated_class'].value_counts()
            for cls, count in class_counts.items():
                percentage = count / len(df) * 100
                f.write(f"{cls}类水质：{count} 条记录 ({percentage:.1f}%)\n")

            drinkable_rate = class_counts[class_counts.index.isin(['I', 'II', 'III'])].sum() / len(df) * 100
            f.write(f"\n可饮用水比例（I-III类）：{drinkable_rate:.1f}%\n")
            f.write(f"污染水比例（IV-劣V类）：{100 - drinkable_rate:.1f}%\n\n")

            # 空间分布特征
            f.write("4. 空间分布特征\n")
            f.write("-" * 40 + "\n")
            station_wqi = df.groupby('station')['wqi'].mean()
            best_station = station_wqi.idxmax()
            worst_station = station_wqi.idxmin()
            f.write(f"水质最佳站点：{best_station} (WQI={station_wqi[best_station]:.1f})\n")
            f.write(f"水质最差站点：{worst_station} (WQI={station_wqi[worst_station]:.1f})\n\n")

            # 生成的图表文件
            f.write("5. 生成的分析图表\n")
            f.write("-" * 40 + "\n")
            for chart_type, filename in generated_files.items():
                f.write(f"{chart_type}: {filename}\n")

            f.write("\n" + "=" * 80 + "\n")
            f.write(f"报告生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("分析系统：长江水质综合评价分析器（问题1正式版）\n")
            f.write("=" * 80 + "\n")

        logger.info(f"正式分析报告已保存：{report_filename}")

        # 4. 输出结果
        print("\n" + "=" * 80)
        print("长江水质综合评价分析完成！（问题1正式版）")
        print("=" * 80)
        print(f"成功生成 {len(generated_files)} 个正式分析图表：")
        for chart_type, filename in generated_files.items():
            print(f"  - {chart_type}: {filename}")
        print(f"\n正式分析报告：{report_filename}")
        print("=" * 80)

        return generated_files, report_filename

    except Exception as e:
        logger.error(f"分析过程中发生错误：{e}")
        raise

if __name__ == "__main__":
    main()
