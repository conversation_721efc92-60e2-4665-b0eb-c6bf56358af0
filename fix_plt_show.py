#!/usr/bin/env python3
"""
批量替换plt.show()为plt.close()的脚本
"""

def fix_plt_show(filename):
    """修复文件中的plt.show()调用"""
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换plt.show()为plt.close()
    content = content.replace('plt.show()', 'plt.close()')
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已修复文件：{filename}")

if __name__ == "__main__":
    fix_plt_show('yangtze_enhanced_analyzer.py')
