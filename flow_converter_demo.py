#!/usr/bin/env python3
"""
长江水流数据转换器演示程序

演示如何使用YangtzeFlowDataConverter将Word表格数据转换为CSV格式
"""

from yangtze_flow_converter import YangtzeFlowDataConverter
import pandas as pd


def main():
    """主演示函数"""
    print("=" * 80)
    print("长江干流主要观测站点水流数据转换器演示")
    print("数据范围：2004年4月-2005年4月（13个月，7个站点）")
    print("=" * 80)
    
    # 创建转换器实例
    converter = YangtzeFlowDataConverter()
    
    print("\n1. 数据转换和导出...")
    
    # 执行完整处理流程
    results = converter.process_all()
    
    print(f"\n2. 处理结果摘要：")
    print(f"   - 总记录数：{results['total_records']}条")
    print(f"   - 处理时间：{results['processing_time']}")
    print(f"   - 数据质量问题：{len(results['validation_results']['data_quality_issues'])}个")
    
    print(f"\n3. 生成的文件：")
    for file_type, file_path in results['exported_files'].items():
        print(f"   - {file_type}: {file_path}")
    print(f"   - 摘要报告: {results['report_file']}")
    
    print(f"\n4. 数据预览（长格式前10行）：")
    long_df = converter.convert_to_long_format()
    print(long_df.head(10).to_string(index=False))
    
    print(f"\n5. 站点距离信息：")
    station_distances = long_df[['station', 'distance_km', 'station_full_name']].drop_duplicates()
    station_distances = station_distances.sort_values('distance_km')
    print(station_distances.to_string(index=False))
    
    print(f"\n6. 月度水流量统计：")
    monthly_stats = long_df.groupby('date').agg({
        'flow_rate_m3s': ['mean', 'min', 'max'],
        'flow_velocity_ms': ['mean', 'min', 'max']
    }).round(2)
    print(monthly_stats)
    
    print(f"\n7. 数据验证结果：")
    validation = results['validation_results']
    print(f"   - 时间范围：{validation['date_range']}")
    print(f"   - 站点数量：{validation['stations_count']}")
    print(f"   - 缺失值总数：{sum(validation['missing_values'].values())}")
    
    if validation['data_quality_issues']:
        print(f"   - 质量问题：")
        for issue in validation['data_quality_issues']:
            print(f"     * {issue}")
    else:
        print(f"   - 数据质量：良好，无发现问题")
    
    print(f"\n8. 与水质数据的关联性：")
    print("   本水流数据的站点名称已与水质监测数据保持一致：")
    for station, info in converter.stations_info.items():
        print(f"   - {station} -> {info['full_name']}")
    
    print("\n" + "=" * 80)
    print("数据转换完成！")
    print("主要输出文件：")
    print("1. yangtze_flow_data_long.csv - 长格式数据（推荐用于分析）")
    print("2. yangtze_flow_rate_wide.csv - 水流量宽格式数据")
    print("3. yangtze_flow_velocity_wide.csv - 水流速宽格式数据")
    print("4. yangtze_flow_data_summary.txt - 详细分析报告")
    print("=" * 80)


if __name__ == "__main__":
    main()
