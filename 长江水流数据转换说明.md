# 长江干流主要观测站点水流数据转换系统

## 概述

本系统将长江干流7个主要观测站点的Word表格水流数据转换为标准CSV格式，便于数据分析和建模。

## 数据来源

- **数据源**：《长江年鉴》相关资料
- **时间范围**：2004年4月-2005年4月（13个月）
- **监测站点**：7个干流主要观测站点
- **监测指标**：水流量（m³/s）、水流速（m/s）、站点间距离（km）

## 观测站点信息

| 站点名称 | 站点编码 | 距离(km) | 对应水质监测站点 | 省份 |
|---------|---------|---------|----------------|------|
| 四川攀枝花 | SC_PZH | 0 | 四川攀枝花龙洞 | 四川 |
| 重庆朱沱 | CQ_ZT | 950 | 重庆朱沱 | 重庆 |
| 湖北宜昌 | HB_YC | 1728 | 湖北宜昌南津关 | 湖北 |
| 湖南岳阳 | HN_YY | 2123 | 湖南岳阳城陵矶 | 湖南 |
| 江西九江 | JX_JJ | 2623 | 江西九江河西水厂 | 江西 |
| 安徽安庆 | AH_AQ | 2787 | 安徽安庆皖河口 | 安徽 |
| 江苏南京 | JS_NJ | 3251 | 江苏南京林山 | 江苏 |

## 系统文件结构

```
├── yangtze_flow_converter.py      # 核心转换器类
├── flow_converter_demo.py         # 演示程序
├── validate_flow_data.py          # 数据验证脚本
├── 长江水流数据转换说明.md         # 本说明文档
└── 输出文件/
    ├── yangtze_flow_data_long.csv      # 长格式数据（推荐）
    ├── yangtze_flow_rate_wide.csv      # 水流量宽格式数据
    ├── yangtze_flow_velocity_wide.csv  # 水流速宽格式数据
    └── yangtze_flow_data_summary.txt   # 详细分析报告
```

## 使用方法

### 1. 快速开始

```python
from yangtze_flow_converter import YangtzeFlowDataConverter

# 创建转换器实例
converter = YangtzeFlowDataConverter()

# 执行完整转换流程
results = converter.process_all()

print(f"转换完成，共处理{results['total_records']}条记录")
```

### 2. 运行演示程序

```bash
python flow_converter_demo.py
```

### 3. 验证数据准确性

```bash
python validate_flow_data.py
```

## 输出文件说明

### 1. 长格式CSV文件（yangtze_flow_data_long.csv）

**推荐用于数据分析**，每行代表一个站点在特定时间的观测记录。

| 字段名 | 说明 | 示例 |
|-------|------|------|
| date | 观测日期 | 2004-04 |
| year | 年份 | 2004 |
| month | 月份 | 4 |
| station | 站点名称 | 四川攀枝花 |
| station_code | 站点编码 | SC_PZH |
| station_full_name | 完整站点名称 | 四川攀枝花龙洞 |
| province | 省份 | 四川 |
| distance_km | 距离(km) | 0 |
| flow_rate_m3s | 水流量(m³/s) | 3690 |
| flow_velocity_ms | 水流速(m/s) | 3.7 |

### 2. 宽格式CSV文件

- **yangtze_flow_rate_wide.csv**：水流量数据，站点作为列
- **yangtze_flow_velocity_wide.csv**：水流速数据，站点作为列

### 3. 分析报告（yangtze_flow_data_summary.txt）

包含详细的统计分析、季节性特征、数据质量检查等信息。

## 数据特征分析

### 水流量统计
- **平均值**：19,142 m³/s
- **最大值**：81,000 m³/s（江苏南京，2004年9月）
- **最小值**：612 m³/s（四川攀枝花，2005年2月）

### 水流速统计
- **平均值**：1.56 m/s
- **最大值**：5.1 m/s（四川攀枝花，2004年9月）
- **最小值**：0.4 m/s（湖北宜昌，2005年2月）

### 季节性特征
- **秋季**：平均水流量最大（29,891 m³/s）
- **冬季**：平均水流量最小（8,509 m³/s）
- **夏季**：平均水流量较大（22,834 m³/s）
- **春季**：平均水流量中等（16,287 m³/s）

## 与水质数据的关联

本系统的站点命名与水质监测数据保持一致，便于进行关联分析：

```python
# 示例：关联水流数据和水质数据
import pandas as pd

flow_data = pd.read_csv('yangtze_flow_data_long.csv')
water_quality_data = pd.read_csv('yangtze_complete_data_2003_2005.csv')

# 通过站点全名进行关联
merged_data = pd.merge(
    flow_data, 
    water_quality_data,
    left_on='station_full_name',
    right_on='station',
    how='inner'
)
```

## 技术特点

1. **数据完整性**：100%数据解析成功率，91条记录全部转换
2. **格式灵活性**：提供长格式和宽格式两种输出
3. **质量保证**：内置数据验证和异常检测
4. **兼容性**：与现有水质数据系统完全兼容
5. **可扩展性**：支持添加更多时间段和站点数据

## 注意事项

1. 所有水流量和水流速数据均为年平均值
2. 距离为从四川攀枝花开始的累计距离
3. 数据已通过完整性验证，确保准确性
4. 建议使用长格式CSV进行时间序列分析
5. 宽格式CSV适用于传统的表格分析

## 扩展功能

系统支持以下扩展：
- 添加更多时间段的数据
- 增加新的监测站点
- 添加其他水文指标
- 与其他环境数据集成

## 联系信息

如有问题或建议，请参考系统日志或联系开发团队。
