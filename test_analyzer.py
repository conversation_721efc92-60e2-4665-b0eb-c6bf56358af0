#!/usr/bin/env python3
"""
测试长江水质综合评价分析系统
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def test_data_loading():
    """测试数据加载"""
    print("测试数据加载...")
    
    df = pd.read_csv('yangtze_complete_data_2003_2005.csv')
    print(f"原始数据：{len(df)}条记录")
    print(f"站点数量：{df['station'].nunique()}")
    print(f"时间范围：{df['date'].min()} - {df['date'].max()}")
    
    # 转换日期
    df['date'] = pd.to_datetime(df['date'])
    
    # 数据清洗
    df_clean = df.dropna(subset=['ph', 'do', 'codmn', 'nh3n'])
    print(f"清洗后数据：{len(df_clean)}条记录")
    
    # 检查数据类型
    print("\n数据类型检查：")
    print(df_clean[['ph', 'do', 'codmn', 'nh3n']].dtypes)
    
    # 检查数据范围
    print("\n数据范围检查：")
    print(df_clean[['ph', 'do', 'codmn', 'nh3n']].describe())
    
    return df_clean

def test_wqi_calculation(df):
    """测试WQI计算"""
    print("\n测试WQI计算...")
    
    # 水质类别评分
    class_scores = {
        'I': 100, 'II': 80, 'III': 60, 'IV': 40, 'V': 20, '劣V': 0
    }
    
    def classify_water_quality(row):
        """简化的水质分类"""
        ph, do, codmn, nh3n = row['ph'], row['do'], row['codmn'], row['nh3n']
        
        # pH值检查
        if ph < 6.0 or ph > 9.0:
            return '劣V'
        
        # 按最差指标确定类别
        if do < 2.0 or codmn > 15.0 or nh3n > 2.0:
            return '劣V'
        elif do < 3.0 or codmn > 10.0 or nh3n > 1.5:
            return 'V'
        elif do < 5.0 or codmn > 6.0 or nh3n > 1.0:
            return 'IV'
        elif do < 6.0 or codmn > 4.0 or nh3n > 0.5:
            return 'III'
        elif do < 7.5 or codmn > 2.0 or nh3n > 0.15:
            return 'II'
        else:
            return 'I'
    
    # 计算水质类别
    df['calculated_class'] = df.apply(classify_water_quality, axis=1)
    
    # 计算WQI
    df['wqi'] = df['calculated_class'].map(class_scores)
    
    print(f"WQI计算完成，平均WQI：{df['wqi'].mean():.1f}")
    
    # 水质类别分布
    class_dist = df['calculated_class'].value_counts()
    print("\n水质类别分布：")
    for cls, count in class_dist.items():
        print(f"  {cls}类：{count}条 ({count/len(df)*100:.1f}%)")
    
    return df

def test_temporal_analysis(df):
    """测试时间分析"""
    print("\n测试时间分析...")
    
    try:
        # 按月份统计
        df['year_month'] = df['date'].dt.to_period('M')
        monthly_stats = df.groupby('year_month')['wqi'].mean()
        print(f"月度统计完成，共{len(monthly_stats)}个月")
        
        # 季节性分析
        df['season'] = df['date'].dt.month.map({
            12: '冬季', 1: '冬季', 2: '冬季',
            3: '春季', 4: '春季', 5: '春季',
            6: '夏季', 7: '夏季', 8: '夏季',
            9: '秋季', 10: '秋季', 11: '秋季'
        })
        
        seasonal_stats = df.groupby('season')['wqi'].mean()
        print("季节性统计：")
        for season, wqi in seasonal_stats.items():
            print(f"  {season}：{wqi:.1f}")
        
        return True
    except Exception as e:
        print(f"时间分析出错：{e}")
        return False

def create_simple_visualization(df):
    """创建简单的可视化"""
    print("\n创建可视化图表...")
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    try:
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('长江水质综合评价分析 - 问题(1)', fontsize=16, fontweight='bold')
        
        # 1. WQI时间趋势
        df['year_month'] = df['date'].dt.to_period('M')
        monthly_wqi = df.groupby('year_month')['wqi'].mean()
        
        axes[0, 0].plot(range(len(monthly_wqi)), monthly_wqi.values, marker='o', linewidth=2)
        axes[0, 0].set_title('水质综合指数(WQI)时间变化趋势')
        axes[0, 0].set_xlabel('时间')
        axes[0, 0].set_ylabel('WQI值')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 各站点WQI对比
        station_wqi = df.groupby('station')['wqi'].mean().sort_values()
        colors = ['red' if x < 40 else 'orange' if x < 60 else 'yellow' if x < 80 else 'green' 
                 for x in station_wqi.values]
        
        axes[0, 1].barh(range(len(station_wqi)), station_wqi.values, color=colors, alpha=0.7)
        axes[0, 1].set_yticks(range(len(station_wqi)))
        axes[0, 1].set_yticklabels([s[:8] + '...' if len(s) > 8 else s for s in station_wqi.index], fontsize=8)
        axes[0, 1].set_title('各地区水质综合指数(WQI)对比')
        axes[0, 1].set_xlabel('WQI值')
        axes[0, 1].grid(True, alpha=0.3, axis='x')
        
        # 3. 水质类别分布
        class_counts = df['calculated_class'].value_counts()
        colors_pie = ['green', 'lightgreen', 'yellow', 'orange', 'red', 'darkred']
        
        axes[1, 0].pie(class_counts.values, labels=class_counts.index,
                      colors=colors_pie[:len(class_counts)], autopct='%1.1f%%', startangle=90)
        axes[1, 0].set_title('水质类别分布')
        
        # 4. 季节性变化
        df['season'] = df['date'].dt.month.map({
            12: '冬季', 1: '冬季', 2: '冬季',
            3: '春季', 4: '春季', 5: '春季',
            6: '夏季', 7: '夏季', 8: '夏季',
            9: '秋季', 10: '秋季', 11: '秋季'
        })
        
        seasonal_wqi = df.groupby('season')['wqi'].mean()
        colors_season = ['lightblue', 'lightgreen', 'orange', 'brown']
        
        axes[1, 1].bar(seasonal_wqi.index, seasonal_wqi.values, 
                      color=colors_season, alpha=0.7)
        axes[1, 1].set_title('季节性水质变化')
        axes[1, 1].set_ylabel('平均WQI值')
        axes[1, 1].grid(True, alpha=0.3, axis='y')
        
        plt.tight_layout()
        
        # 保存图表
        filename = f'问题1_水质综合评价_{timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"图表已保存：{filename}")
        plt.show()
        
        return True
    except Exception as e:
        print(f"可视化创建出错：{e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("长江水质综合评价分析系统测试")
    print("=" * 60)
    
    # 1. 测试数据加载
    df = test_data_loading()
    
    # 2. 测试WQI计算
    df = test_wqi_calculation(df)
    
    # 3. 测试时间分析
    temporal_ok = test_temporal_analysis(df)
    
    # 4. 创建可视化
    if temporal_ok:
        viz_ok = create_simple_visualization(df)
        
        if viz_ok:
            print("\n" + "=" * 60)
            print("测试完成！所有功能正常运行。")
            print("=" * 60)
            
            # 输出基本统计
            overall_wqi = df['wqi'].mean()
            pollution_rate = (df['calculated_class'].isin(['IV', 'V', '劣V'])).mean() * 100
            
            print(f"整体水质综合指数(WQI)：{overall_wqi:.1f}")
            print(f"污染水比例：{pollution_rate:.1f}%")
        else:
            print("可视化创建失败")
    else:
        print("时间分析失败")

if __name__ == "__main__":
    main()
